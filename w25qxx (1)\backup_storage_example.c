#include "Components/backup_storage/storage_manager.h"
#include "Components/backup_storage/rtc_backup.h"
#include <stdio.h>
#include <string.h>

/**
 * @brief 掉电数据保存方案使用示例
 * @note 演示如何使用各种存储方案保存关键数据
 */

/* 示例用户数据结构 */
typedef struct {
    uint32_t system_mode;       /* 系统模式 */
    uint32_t motor_speed;       /* 电机转速 */
    uint32_t sensor_values[8];  /* 传感器数值 */
    uint32_t error_flags;       /* 错误标志 */
    uint32_t calibration_data[4]; /* 校准数据 */
    char device_name[16];       /* 设备名称 */
} user_config_t;

/**
 * @brief 方案一：RTC备份寄存器 (推荐小数据量)
 */
void example_rtc_backup_usage(void)
{
    printf("=== RTC备份寄存器示例 ===\r\n");
    
    /* 初始化RTC备份系统 */
    if (backup_init() != HAL_OK) {
        printf("RTC备份初始化失败\r\n");
        return;
    }
    
    /* 准备要保存的数据 */
    backup_data_t config;
    config.system_config = 0x12345678;
    config.user_settings[0] = 1000;  /* 电机转速 */
    config.user_settings[1] = 2000;  /* 传感器阈值 */
    config.sensor_data[0] = 123;     /* 传感器1数值 */
    config.sensor_data[1] = 456;     /* 传感器2数值 */
    config.status_flags = 0xABCD;
    config.timestamp = HAL_GetTick();
    
    /* 保存数据 */
    if (backup_write_data(&config) == HAL_OK) {
        printf("✓ 数据已保存到RTC备份寄存器\r\n");
    } else {
        printf("✗ 数据保存失败\r\n");
    }
    
    /* 模拟系统重启后读取数据 */
    backup_data_t restored_config;
    if (backup_read_data(&restored_config) == HAL_OK) {
        printf("✓ 数据恢复成功:\r\n");
        printf("  系统配置: 0x%08X\r\n", restored_config.system_config);
        printf("  电机转速: %d\r\n", restored_config.user_settings[0]);
        printf("  传感器阈值: %d\r\n", restored_config.user_settings[1]);
        printf("  状态标志: 0x%04X\r\n", restored_config.status_flags);
    } else {
        printf("✗ 数据恢复失败\r\n");
    }
}

/**
 * @brief 方案二：统一存储管理器使用
 */
void example_storage_manager_usage(void)
{
    printf("=== 统一存储管理器示例 ===\r\n");
    
    /* 初始化存储管理器 */
    if (storage_manager_init() != STORAGE_OK) {
        printf("存储管理器初始化失败\r\n");
        return;
    }
    
    /* 准备用户数据 */
    user_config_t user_data;
    user_data.system_mode = 1;
    user_data.motor_speed = 1500;
    user_data.sensor_values[0] = 100;
    user_data.sensor_values[1] = 200;
    user_data.error_flags = 0;
    strcpy(user_data.device_name, "STM32F407");
    
    /* 自动选择最佳存储方案 */
    storage_type_t best_storage = storage_auto_select(sizeof(user_data));
    printf("推荐存储方案: %d\r\n", best_storage);
    
    /* 设置主存储 */
    storage_set_primary(best_storage);
    
    /* 保存数据 */
    if (storage_write_data((uint8_t*)&user_data, sizeof(user_data)) == STORAGE_OK) {
        printf("✓ 数据保存成功\r\n");
    }
    
    /* 读取数据 */
    user_config_t restored_data;
    uint32_t read_size = sizeof(restored_data);
    if (storage_read_data((uint8_t*)&restored_data, &read_size) == STORAGE_OK) {
        printf("✓ 数据恢复成功:\r\n");
        printf("  系统模式: %d\r\n", restored_data.system_mode);
        printf("  电机转速: %d RPM\r\n", restored_data.motor_speed);
        printf("  设备名称: %s\r\n", restored_data.device_name);
    }
}

/**
 * @brief 方案三：多重备份保护
 */
void example_multiple_backup(void)
{
    printf("=== 多重备份保护示例 ===\r\n");
    
    user_config_t critical_data;
    critical_data.system_mode = 2;
    critical_data.motor_speed = 2000;
    critical_data.error_flags = 0x1234;
    
    /* 使用RTC备份作为主存储，内部Flash作为备份 */
    storage_status_t result = storage_write_with_backup(
        (uint8_t*)&critical_data, 
        sizeof(critical_data),
        STORAGE_TYPE_RTC_BACKUP,        /* 主存储 */
        STORAGE_TYPE_INTERNAL_FLASH     /* 备份存储 */
    );
    
    if (result == STORAGE_OK) {
        printf("✓ 关键数据已保存到主存储和备份存储\r\n");
    }
    
    /* 读取时自动从备份恢复 */
    user_config_t recovered_data;
    uint32_t size = sizeof(recovered_data);
    result = storage_read_with_fallback(
        (uint8_t*)&recovered_data,
        &size,
        STORAGE_TYPE_RTC_BACKUP,        /* 优先从主存储读取 */
        STORAGE_TYPE_INTERNAL_FLASH     /* 主存储失败时从备份读取 */
    );
    
    if (result == STORAGE_OK) {
        printf("✓ 数据恢复成功 (可能来自备份)\r\n");
    }
}

/**
 * @brief 掉电保护的完整应用示例
 */
void power_loss_protection_demo(void)
{
    printf("=== 掉电保护完整方案 ===\r\n");
    
    /* 1. 系统启动时检查是否有保存的数据 */
    if (backup_is_data_valid()) {
        printf("检测到有效的备份数据，正在恢复...\r\n");
        
        backup_data_t saved_data;
        if (backup_read_data(&saved_data) == HAL_OK) {
            /* 恢复系统状态 */
            printf("系统状态已恢复:\r\n");
            printf("  配置: 0x%08X\r\n", saved_data.system_config);
            printf("  时间戳: %d\r\n", saved_data.timestamp);
        }
    } else {
        printf("未检测到有效备份数据，使用默认配置\r\n");
    }
    
    /* 2. 运行时定期保存关键数据 */
    backup_data_t current_state;
    current_state.system_config = 0x87654321;
    current_state.user_settings[0] = 1800;  /* 当前电机转速 */
    current_state.sensor_data[0] = 789;     /* 当前传感器读数 */
    current_state.timestamp = HAL_GetTick();
    
    /* 保存当前状态 */
    backup_write_data(&current_state);
    printf("当前状态已保存，掉电后可恢复\r\n");
    
    /* 3. 关键操作前保存状态 */
    printf("执行关键操作前保存状态...\r\n");
    current_state.status_flags = 0x8000;  /* 标记正在执行关键操作 */
    backup_write_data(&current_state);
    
    /* 模拟关键操作 */
    HAL_Delay(100);
    
    /* 关键操作完成，清除标志 */
    current_state.status_flags = 0x0000;
    backup_write_data(&current_state);
    printf("关键操作完成，状态已更新\r\n");
}

/**
 * @brief 存储方案选择指南
 */
void storage_selection_guide(void)
{
    printf("\r\n=== 存储方案选择指南 ===\r\n");
    printf("1. RTC备份寄存器 (80字节):\r\n");
    printf("   ✓ 优点: 最快速度，最低功耗，只需纽扣电池\r\n");
    printf("   ✗ 缺点: 容量小\r\n");
    printf("   适用: 系统配置、状态标志、小量关键数据\r\n\r\n");
    
    printf("2. 内部Flash (16KB-128KB):\r\n");
    printf("   ✓ 优点: 容量大，无需外部器件\r\n");
    printf("   ✗ 缺点: 擦写次数有限，速度较慢\r\n");
    printf("   适用: 配置文件、日志数据、固件参数\r\n\r\n");
    
    printf("3. 外部EEPROM (1KB-64KB):\r\n");
    printf("   ✓ 优点: 专为数据存储设计，擦写次数多\r\n");
    printf("   ✗ 缺点: 需要额外芯片和I2C接口\r\n");
    printf("   适用: 用户设置、校准数据、历史记录\r\n\r\n");
    
    printf("4. 外部FRAM (4KB-2MB):\r\n");
    printf("   ✓ 优点: 无限擦写，掉电立即保存\r\n");
    printf("   ✗ 缺点: 成本高，需要SPI接口\r\n");
    printf("   适用: 实时数据、频繁更新的状态\r\n\r\n");
    
    printf("推荐组合方案:\r\n");
    printf("- 小系统: RTC备份寄存器 + 内部Flash\r\n");
    printf("- 中等系统: RTC备份寄存器 + EEPROM\r\n");
    printf("- 高端系统: RTC备份寄存器 + FRAM + 内部Flash\r\n");
}

/**
 * @brief 主测试函数
 */
void backup_storage_test_all(void)
{
    printf("\r\n========== 掉电数据保存方案测试 ==========\r\n");
    
    /* 显示选择指南 */
    storage_selection_guide();
    
    /* 测试RTC备份寄存器 */
    example_rtc_backup_usage();
    printf("\r\n");
    
    /* 测试统一存储管理器 */
    example_storage_manager_usage();
    printf("\r\n");
    
    /* 测试多重备份 */
    example_multiple_backup();
    printf("\r\n");
    
    /* 演示完整的掉电保护方案 */
    power_loss_protection_demo();
    
    printf("\r\n========== 测试完成 ==========\r\n");
}

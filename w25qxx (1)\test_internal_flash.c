#include "Components/backup_storage/internal_flash.h"
#include <stdio.h>
#include <string.h>

/**
 * @brief 内部Flash存储测试程序
 * @note 替代外部Flash的数据保存方案
 */

/* 用户数据结构示例 */
typedef struct {
    uint32_t system_mode;       /* 系统工作模式 */
    uint32_t motor_speed;       /* 电机转速设定 */
    uint32_t sensor_threshold;  /* 传感器阈值 */
    uint32_t error_flags;       /* 错误标志位 */
    uint32_t calibration[4];    /* 校准参数 */
    char device_id[16];         /* 设备ID */
    uint32_t boot_count;        /* 启动次数 */
} user_config_t;

/**
 * @brief 测试内部Flash基本功能
 */
void test_internal_flash_basic(void)
{
    printf("\r\n=== 内部Flash基本功能测试 ===\r\n");
    
    /* 1. 初始化 */
    printf("1. 初始化内部Flash存储...\r\n");
    if (internal_flash_init() != HAL_OK) {
        printf("   ✗ 初始化失败\r\n");
        return;
    }
    printf("   ✓ 初始化成功\r\n");
    
    /* 2. 准备测试数据 */
    user_config_t config;
    config.system_mode = 1;
    config.motor_speed = 1500;
    config.sensor_threshold = 2048;
    config.error_flags = 0;
    config.calibration[0] = 100;
    config.calibration[1] = 200;
    config.calibration[2] = 300;
    config.calibration[3] = 400;
    strcpy(config.device_id, "DEV_001");
    config.boot_count = 1;
    
    printf("2. 准备测试数据 (大小: %d字节)\r\n", (int)sizeof(config));
    
    /* 3. 写入数据 */
    printf("3. 写入数据到Flash...\r\n");
    if (internal_flash_write_data((uint8_t*)&config, sizeof(config)) == HAL_OK) {
        printf("   ✓ 数据写入成功\r\n");
    } else {
        printf("   ✗ 数据写入失败\r\n");
        return;
    }
    
    /* 4. 验证数据 */
    printf("4. 验证Flash数据有效性...\r\n");
    if (internal_flash_is_data_valid()) {
        printf("   ✓ 数据有效性验证通过\r\n");
    } else {
        printf("   ✗ 数据有效性验证失败\r\n");
        return;
    }
    
    /* 5. 读取数据 */
    printf("5. 从Flash读取数据...\r\n");
    user_config_t read_config;
    uint32_t read_size = sizeof(read_config);
    
    if (internal_flash_read_data((uint8_t*)&read_config, &read_size) == HAL_OK) {
        printf("   ✓ 数据读取成功\r\n");
        printf("   读取大小: %d字节\r\n", (int)read_size);
    } else {
        printf("   ✗ 数据读取失败\r\n");
        return;
    }
    
    /* 6. 验证数据一致性 */
    printf("6. 验证数据一致性...\r\n");
    if (memcmp(&config, &read_config, sizeof(config)) == 0) {
        printf("   ✓ 数据一致性验证通过\r\n");
        printf("   系统模式: %d\r\n", (int)read_config.system_mode);
        printf("   电机转速: %d RPM\r\n", (int)read_config.motor_speed);
        printf("   设备ID: %s\r\n", read_config.device_id);
        printf("   启动次数: %d\r\n", (int)read_config.boot_count);
    } else {
        printf("   ✗ 数据一致性验证失败\r\n");
    }
    
    printf("=== 基本功能测试完成 ===\r\n");
}

/**
 * @brief 测试掉电恢复场景
 */
void test_power_loss_recovery(void)
{
    printf("\r\n=== 掉电恢复场景测试 ===\r\n");
    
    /* 初始化 */
    internal_flash_init();
    
    /* 模拟系统启动时检查是否有保存的数据 */
    printf("1. 系统启动，检查Flash中是否有保存的数据...\r\n");
    
    if (internal_flash_is_data_valid()) {
        printf("   ✓ 检测到有效的保存数据，正在恢复...\r\n");
        
        user_config_t saved_config;
        uint32_t size = sizeof(saved_config);
        
        if (internal_flash_read_data((uint8_t*)&saved_config, &size) == HAL_OK) {
            printf("   ✓ 系统状态恢复成功:\r\n");
            printf("     - 系统模式: %d\r\n", (int)saved_config.system_mode);
            printf("     - 电机转速: %d RPM\r\n", (int)saved_config.motor_speed);
            printf("     - 传感器阈值: %d\r\n", (int)saved_config.sensor_threshold);
            printf("     - 设备ID: %s\r\n", saved_config.device_id);
            printf("     - 启动次数: %d\r\n", (int)saved_config.boot_count);
            
            /* 更新启动次数 */
            saved_config.boot_count++;
            printf("2. 更新启动次数并保存...\r\n");
            
            if (internal_flash_write_data((uint8_t*)&saved_config, sizeof(saved_config)) == HAL_OK) {
                printf("   ✓ 启动次数已更新为: %d\r\n", (int)saved_config.boot_count);
            }
        }
    } else {
        printf("   ! 未检测到有效数据，使用默认配置\r\n");
        
        /* 创建默认配置 */
        user_config_t default_config;
        default_config.system_mode = 0;
        default_config.motor_speed = 1000;
        default_config.sensor_threshold = 1024;
        default_config.error_flags = 0;
        memset(default_config.calibration, 0, sizeof(default_config.calibration));
        strcpy(default_config.device_id, "NEW_DEVICE");
        default_config.boot_count = 1;
        
        printf("2. 保存默认配置...\r\n");
        if (internal_flash_write_data((uint8_t*)&default_config, sizeof(default_config)) == HAL_OK) {
            printf("   ✓ 默认配置已保存\r\n");
        }
    }
    
    printf("=== 掉电恢复测试完成 ===\r\n");
}

/**
 * @brief 测试关键操作保护
 */
void test_critical_operation_protection(void)
{
    printf("\r\n=== 关键操作保护测试 ===\r\n");
    
    internal_flash_init();
    
    /* 读取当前配置 */
    user_config_t config;
    uint32_t size = sizeof(config);
    
    if (internal_flash_read_data((uint8_t*)&config, &size) == HAL_OK) {
        printf("1. 当前配置读取成功\r\n");
    } else {
        /* 使用默认配置 */
        printf("1. 使用默认配置\r\n");
        memset(&config, 0, sizeof(config));
        config.system_mode = 1;
        config.motor_speed = 1200;
        strcpy(config.device_id, "CRITICAL_TEST");
    }
    
    /* 模拟关键操作前保存状态 */
    printf("2. 执行关键操作前保存当前状态...\r\n");
    config.error_flags |= 0x8000;  /* 设置"正在执行关键操作"标志 */
    
    if (internal_flash_write_data((uint8_t*)&config, sizeof(config)) == HAL_OK) {
        printf("   ✓ 关键操作前状态已保存\r\n");
    }
    
    /* 模拟关键操作 */
    printf("3. 执行关键操作 (模拟延时)...\r\n");
    HAL_Delay(100);  /* 模拟关键操作耗时 */
    
    /* 关键操作完成，清除标志 */
    printf("4. 关键操作完成，更新状态...\r\n");
    config.error_flags &= ~0x8000;  /* 清除"正在执行关键操作"标志 */
    config.motor_speed = 1800;      /* 更新电机转速 */
    
    if (internal_flash_write_data((uint8_t*)&config, sizeof(config)) == HAL_OK) {
        printf("   ✓ 关键操作完成状态已保存\r\n");
        printf("   新的电机转速: %d RPM\r\n", (int)config.motor_speed);
    }
    
    printf("=== 关键操作保护测试完成 ===\r\n");
}

/**
 * @brief 主测试函数
 */
void test_internal_flash_all(void)
{
    printf("\r\n========== 内部Flash存储测试开始 ==========\r\n");
    printf("STM32F407内部Flash存储方案\r\n");
    printf("- 使用扇区11 (128KB) 存储用户数据\r\n");
    printf("- 支持数据完整性校验\r\n");
    printf("- 掉电后数据永久保存\r\n");
    printf("================================================\r\n");
    
    /* 执行各项测试 */
    test_internal_flash_basic();
    test_power_loss_recovery();
    test_critical_operation_protection();
    
    printf("\r\n========== 所有测试完成 ==========\r\n");
    printf("内部Flash存储方案已就绪！\r\n");
    printf("您可以在main函数中调用 test_internal_flash_all() 进行测试\r\n");
}

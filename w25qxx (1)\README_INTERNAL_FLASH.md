# STM32F407 内部Flash存储方案

## 概述
当外部SPI Flash损坏时，使用STM32F407内部Flash的最后一个扇区(扇区11, 128KB)来存储关键数据，实现掉电数据保持功能。

## 特性
- ✅ **大容量存储**: 128KB存储空间，足够存储大量配置数据
- ✅ **数据完整性**: 内置校验和验证，确保数据可靠性
- ✅ **掉电保护**: 数据永久保存在Flash中，掉电不丢失
- ✅ **简单易用**: 提供简洁的API接口
- ✅ **无额外成本**: 使用内部资源，无需外部器件

## 文件结构
```
Components/backup_storage/
├── internal_flash.h          # 头文件定义
├── internal_flash.c          # 核心实现
test_internal_flash.c         # 测试程序
main_integration_example.c    # 集成示例
README_INTERNAL_FLASH.md      # 本文档
```

## 快速开始

### 1. 添加文件到项目
将以下文件添加到您的STM32项目中：
- `Components/backup_storage/internal_flash.h`
- `Components/backup_storage/internal_flash.c`

### 2. 包含头文件
```c
#include "Components/backup_storage/internal_flash.h"
```

### 3. 基本使用示例
```c
#include "Components/backup_storage/internal_flash.h"

typedef struct {
    uint32_t motor_speed;
    uint32_t sensor_threshold;
    char device_name[16];
} my_config_t;

void example_usage(void)
{
    my_config_t config;
    
    // 1. 初始化
    internal_flash_init();
    
    // 2. 准备数据
    config.motor_speed = 1500;
    config.sensor_threshold = 2048;
    strcpy(config.device_name, "STM32F407");
    
    // 3. 保存数据
    internal_flash_write_data((uint8_t*)&config, sizeof(config));
    
    // 4. 读取数据 (系统重启后)
    if (internal_flash_is_data_valid()) {
        my_config_t loaded_config;
        uint32_t size = sizeof(loaded_config);
        internal_flash_read_data((uint8_t*)&loaded_config, &size);
        
        printf("恢复的配置: 转速=%d, 阈值=%d, 设备=%s\n", 
               loaded_config.motor_speed, 
               loaded_config.sensor_threshold,
               loaded_config.device_name);
    }
}
```

## API 参考

### 初始化函数
```c
HAL_StatusTypeDef internal_flash_init(void);
```
初始化内部Flash存储系统。

### 数据操作函数
```c
// 写入数据
HAL_StatusTypeDef internal_flash_write_data(const uint8_t* data, uint32_t size);

// 读取数据
HAL_StatusTypeDef internal_flash_read_data(uint8_t* data, uint32_t* size);

// 检查数据有效性
uint8_t internal_flash_is_data_valid(void);

// 擦除扇区
HAL_StatusTypeDef internal_flash_erase_sector(void);
```

### 辅助函数
```c
// 计算校验和
uint32_t internal_flash_calculate_checksum(const uint8_t* data, uint32_t size);

// Flash解锁/锁定
HAL_StatusTypeDef internal_flash_unlock(void);
HAL_StatusTypeDef internal_flash_lock(void);
```

## 系统集成

### 在main函数中集成
```c
int main(void)
{
    // 硬件初始化
    HAL_Init();
    SystemClock_Config();
    MX_GPIO_Init();
    MX_USART1_UART_Init();
    
    // 加载保存的配置
    if (internal_flash_init() == HAL_OK) {
        if (internal_flash_is_data_valid()) {
            // 恢复保存的配置
            load_saved_config();
        } else {
            // 使用默认配置
            use_default_config();
        }
    }
    
    while (1) {
        // 应用逻辑
        
        // 配置更改时保存
        if (config_changed) {
            save_current_config();
            config_changed = 0;
        }
        
        HAL_Delay(100);
    }
}
```

## 使用场景

### 1. 系统配置保存
```c
typedef struct {
    uint32_t system_mode;
    uint32_t motor_speed;
    uint32_t sensor_config[4];
    char device_id[16];
} system_config_t;

// 保存系统配置
void save_system_config(const system_config_t* config) {
    internal_flash_write_data((uint8_t*)config, sizeof(system_config_t));
}

// 加载系统配置
int load_system_config(system_config_t* config) {
    if (internal_flash_is_data_valid()) {
        uint32_t size = sizeof(system_config_t);
        return internal_flash_read_data((uint8_t*)config, &size) == HAL_OK ? 0 : -1;
    }
    return -1;
}
```

### 2. 关键操作保护
```c
void critical_operation(void) {
    // 操作前保存状态
    system_state.flags |= CRITICAL_OPERATION_FLAG;
    internal_flash_write_data((uint8_t*)&system_state, sizeof(system_state));
    
    // 执行关键操作
    perform_critical_task();
    
    // 操作完成，清除标志
    system_state.flags &= ~CRITICAL_OPERATION_FLAG;
    internal_flash_write_data((uint8_t*)&system_state, sizeof(system_state));
}
```

### 3. 启动恢复检查
```c
void startup_recovery_check(void) {
    if (internal_flash_is_data_valid()) {
        load_system_state();
        
        if (system_state.flags & CRITICAL_OPERATION_FLAG) {
            printf("检测到异常重启，正在恢复...\n");
            // 执行恢复操作
            recovery_from_critical_operation();
        }
    }
}
```

## 测试程序

运行完整测试：
```c
// 在main函数中调用
test_internal_flash_all();
```

测试包括：
- 基本读写功能测试
- 数据完整性验证
- 掉电恢复场景测试
- 关键操作保护测试

## 技术规格

| 项目 | 规格 |
|------|------|
| 存储容量 | 128KB (扇区11) |
| 数据块大小 | 4KB |
| 最大单次存储 | ~4KB |
| 擦写次数 | 10,000次 (典型值) |
| 数据保持时间 | 20年 (25°C) |
| 写入时间 | ~100ms (擦除+写入) |
| 读取时间 | <1ms |

## 注意事项

1. **擦写次数限制**: 内部Flash有擦写次数限制，避免频繁写入
2. **扇区擦除**: 每次写入前需要擦除整个扇区
3. **数据大小**: 单次存储数据不要超过4KB
4. **电源稳定**: 写入过程中确保电源稳定
5. **备份策略**: 重要数据建议多重备份

## 故障排除

### 常见问题

**Q: Flash写入失败**
A: 检查Flash是否正确解锁，电源是否稳定

**Q: 数据校验失败**
A: 可能是写入过程中断或数据损坏，重新写入数据

**Q: 无法读取数据**
A: 检查魔数和版本号是否匹配

**Q: 系统启动慢**
A: Flash读取速度很快，检查其他初始化代码

## 性能优化建议

1. **减少写入频率**: 批量更新配置，避免单个参数频繁写入
2. **使用缓存**: 在RAM中缓存配置，定期写入Flash
3. **分区管理**: 将不同类型的数据分开存储
4. **压缩数据**: 对大数据进行压缩存储

## 扩展功能

可以基于此方案扩展：
- 多版本配置管理
- 数据压缩存储
- 磨损均衡算法
- 多扇区轮换使用

---

**替代外部Flash的完美方案！** 🚀

现在您的STM32F407即使在外部Flash损坏的情况下，也能可靠地保存关键数据！

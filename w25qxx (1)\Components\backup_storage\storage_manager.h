#ifndef STORAGE_MANAGER_H
#define STORAGE_MANAGER_H

#include "main.h"

/* 统一存储管理接口 */
/* 支持多种存储方案的统一API */

/* 存储类型定义 */
typedef enum {
    STORAGE_TYPE_RTC_BACKUP = 0,    /* RTC备份寄存器 */
    STORAGE_TYPE_INTERNAL_FLASH,    /* 内部Flash */
    STORAGE_TYPE_EEPROM_I2C,        /* I2C EEPROM */
    STORAGE_TYPE_FRAM_SPI,          /* SPI FRAM */
    STORAGE_TYPE_MAX
} storage_type_t;

/* 存储状态 */
typedef enum {
    STORAGE_OK = 0,
    STORAGE_ERROR,
    STORAGE_TIMEOUT,
    STORAGE_FULL,
    STORAGE_INVALID_DATA,
    STORAGE_NOT_INITIALIZED
} storage_status_t;

/* 存储信息结构 */
typedef struct {
    storage_type_t type;        /* 存储类型 */
    uint32_t capacity;          /* 容量(字节) */
    uint32_t available;         /* 可用空间 */
    uint32_t write_cycles;      /* 写入周期限制 */
    uint8_t is_initialized;     /* 是否已初始化 */
    uint8_t is_available;       /* 是否可用 */
    const char* name;           /* 存储名称 */
} storage_info_t;

/* 存储操作接口 */
typedef struct {
    storage_status_t (*init)(void);
    storage_status_t (*write)(const uint8_t* data, uint32_t size);
    storage_status_t (*read)(uint8_t* data, uint32_t* size);
    storage_status_t (*erase)(void);
    uint8_t (*is_valid)(void);
    storage_status_t (*get_info)(storage_info_t* info);
} storage_interface_t;

/* 全局函数声明 */
storage_status_t storage_manager_init(void);
storage_status_t storage_set_primary(storage_type_t type);
storage_status_t storage_write_data(const uint8_t* data, uint32_t size);
storage_status_t storage_read_data(uint8_t* data, uint32_t* size);
storage_status_t storage_backup_data(const uint8_t* data, uint32_t size);
storage_status_t storage_get_info(storage_type_t type, storage_info_t* info);
storage_status_t storage_format(storage_type_t type);
uint8_t storage_is_available(storage_type_t type);

/* 自动选择最佳存储方案 */
storage_type_t storage_auto_select(uint32_t data_size);

/* 多重备份功能 */
storage_status_t storage_write_with_backup(const uint8_t* data, uint32_t size, 
                                          storage_type_t primary, storage_type_t backup);
storage_status_t storage_read_with_fallback(uint8_t* data, uint32_t* size,
                                           storage_type_t primary, storage_type_t fallback);

/* 数据完整性检查 */
storage_status_t storage_verify_all(void);
storage_status_t storage_repair_data(void);

/* 统计信息 */
typedef struct {
    uint32_t total_writes;      /* 总写入次数 */
    uint32_t total_reads;       /* 总读取次数 */
    uint32_t error_count;       /* 错误次数 */
    uint32_t last_write_time;   /* 最后写入时间 */
    uint32_t last_read_time;    /* 最后读取时间 */
} storage_stats_t;

storage_status_t storage_get_stats(storage_type_t type, storage_stats_t* stats);
storage_status_t storage_reset_stats(storage_type_t type);

/* 调试和诊断 */
void storage_print_info(void);
void storage_run_test(storage_type_t type);

#endif /* STORAGE_MANAGER_H */

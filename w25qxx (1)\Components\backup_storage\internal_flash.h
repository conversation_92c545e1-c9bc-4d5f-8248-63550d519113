#ifndef INTERNAL_FLASH_H
#define INTERNAL_FLASH_H

#include "main.h"

/* STM32F407内部Flash存储方案 */
/* 使用最后一个扇区存储用户数据 */

/* STM32F407 Flash扇区定义 */
#define FLASH_SECTOR_0     0x08000000  /* 16KB */
#define FLASH_SECTOR_1     0x08004000  /* 16KB */
#define FLASH_SECTOR_2     0x08008000  /* 16KB */
#define FLASH_SECTOR_3     0x0800C000  /* 16KB */
#define FLASH_SECTOR_4     0x08010000  /* 64KB */
#define FLASH_SECTOR_5     0x08020000  /* 128KB */
#define FLASH_SECTOR_6     0x08040000  /* 128KB */
#define FLASH_SECTOR_7     0x08060000  /* 128KB */
#define FLASH_SECTOR_8     0x08080000  /* 128KB */
#define FLASH_SECTOR_9     0x080A0000  /* 128KB */
#define FLASH_SECTOR_10    0x080C0000  /* 128KB */
#define FLASH_SECTOR_11    0x080E0000  /* 128KB */

/* 使用扇区11作为数据存储区 */
#define USER_DATA_SECTOR        FLASH_SECTOR_11
#define USER_DATA_SECTOR_NUM    FLASH_SECTOR_11
#define USER_DATA_SIZE          (128 * 1024)  /* 128KB */

/* 数据块定义 */
#define DATA_BLOCK_SIZE         4096    /* 4KB per block */
#define MAX_DATA_BLOCKS         (USER_DATA_SIZE / DATA_BLOCK_SIZE)

/* 数据头结构 */
typedef struct {
    uint32_t magic;         /* 魔数 */
    uint32_t version;       /* 版本号 */
    uint32_t data_size;     /* 数据大小 */
    uint32_t checksum;      /* 校验和 */
    uint32_t timestamp;     /* 时间戳 */
    uint32_t reserved[3];   /* 保留字段 */
} flash_data_header_t;

#define FLASH_MAGIC_VALUE   0x87654321
#define FLASH_VERSION       0x0001

/* 用户数据结构 - 可根据需要调整 */
typedef struct {
    flash_data_header_t header;
    uint8_t user_data[DATA_BLOCK_SIZE - sizeof(flash_data_header_t)];
} flash_data_block_t;

/* 函数声明 */
HAL_StatusTypeDef internal_flash_init(void);
HAL_StatusTypeDef internal_flash_write_data(const uint8_t* data, uint32_t size);
HAL_StatusTypeDef internal_flash_read_data(uint8_t* data, uint32_t* size);
HAL_StatusTypeDef internal_flash_erase_sector(void);
uint8_t internal_flash_is_data_valid(void);
uint32_t internal_flash_calculate_checksum(const uint8_t* data, uint32_t size);

/* 辅助函数 */
HAL_StatusTypeDef internal_flash_unlock(void);
HAL_StatusTypeDef internal_flash_lock(void);
uint32_t internal_flash_get_sector(uint32_t address);

#endif /* INTERNAL_FLASH_H */

#include "Components/gd25qxx/gd25qxx.h"
#include <stdio.h>

/**
 * @brief 增强版SPI Flash测试函数
 * @note 包含详细的诊断信息和错误处理
 */
void test_gd25qxx_hal(void)
{
    uint32_t flash_id, ma_id, jed_id;
    uint8_t test_data[256];
    uint8_t read_data[256];
    uint32_t test_address = 0x1000; // 测试地址

    printf("=== SPI Flash 诊断测试开始 ===\r\n");

    // 1. 初始化SPI Flash
    spi_flash_init();
    printf("1. SPI Flash 初始化完成\r\n");

    // 2. 读取多种Flash ID进行诊断
    ma_id = spi_flash_read_ma_id();
    jed_id = spi_flash_read_jed_id();
    flash_id = spi_flash_read_id();

    printf("2. Flash ID 读取结果:\r\n");
    printf("   MA-ID: 0x%04X\r\n", ma_id);
    printf("   JED-ID: 0x%06X\r\n", jed_id);
    printf("   Flash-ID: 0x%06X\r\n", flash_id);

    // 3. ID诊断分析
    if (ma_id == 0xFFFF) {
        printf("   警告: MA-ID异常(0xFFFF)，可能存在硬件连接问题\r\n");
    }

    // 检查常见Flash型号
    if ((jed_id & 0xFFFF00) == 0xC84000) {
        printf("   检测到: GD25Q系列Flash\r\n");
    } else if ((jed_id & 0xFF0000) == 0xEF0000) {
        printf("   检测到: W25Q系列Flash\r\n");
    } else {
        printf("   未知Flash型号，JED-ID: 0x%06X\r\n", jed_id);
    }

    // 4. 读取测试（读取空白区域）
    printf("3. 读取测试 (地址: 0x%06X):\r\n", test_address);
    spi_flash_buffer_read(read_data, test_address, 16); // 只读16字节用于显示

    printf("   读取数据: ");
    for (int i = 0; i < 16; i++) {
        printf("0x%02X ", read_data[i]);
    }
    printf("\r\n");

    // 检查是否为空白区域
    int is_blank = 1;
    for (int i = 0; i < 16; i++) {
        if (read_data[i] != 0xFF) {
            is_blank = 0;
            break;
        }
    }

    if (is_blank) {
        printf("   状态: 空白区域 (0xFF) - 正常\r\n");
        printf("   说明: 之前看到的乱码是0xFF字符的显示效果\r\n");
    } else {
        printf("   状态: 包含数据\r\n");
    }

    // 5. 如果ID正常，进行写入测试
    if (jed_id != 0xFFFFFF && jed_id != 0x000000) {
        printf("4. 执行写入测试:\r\n");

        // 准备测试数据
        for (int i = 0; i < 256; i++) {
            test_data[i] = i;
        }

        // 擦除扇区
        printf("   擦除扇区...\r\n");
        spi_flash_sector_erase(test_address);

        // 写入数据
        printf("   写入测试数据...\r\n");
        spi_flash_page_write(test_data, test_address, 256);

        // 读取数据
        printf("   读取并验证数据...\r\n");
        spi_flash_buffer_read(read_data, test_address, 256);

        // 验证数据
        int verify_ok = 1;
        for (int i = 0; i < 256; i++) {
            if (test_data[i] != read_data[i]) {
                verify_ok = 0;
                printf("   验证失败: 地址0x%06X, 写入0x%02X, 读取0x%02X\r\n",
                       test_address + i, test_data[i], read_data[i]);
                break;
            }
        }

        if (verify_ok) {
            printf("   ✓ 写入测试成功！\r\n");
        } else {
            printf("   ✗ 写入测试失败！\r\n");
        }
    } else {
        printf("4. 跳过写入测试 (Flash ID异常)\r\n");
    }

    printf("=== SPI Flash 测试完成 ===\r\n\r\n");
}

/**
 * @brief 批量数据读写测试
 */
void test_gd25qxx_bulk_rw(void)
{
    uint8_t write_buffer[SPI_FLASH_PAGE_SIZE];
    uint8_t read_buffer[SPI_FLASH_PAGE_SIZE];
    uint32_t test_addr = 0x2000;
    
    // 初始化
    spi_flash_init();
    
    // 准备测试数据
    for (int i = 0; i < SPI_FLASH_PAGE_SIZE; i++) {
        write_buffer[i] = (uint8_t)(i ^ 0xAA);
    }
    
    // 擦除扇区
    spi_flash_sector_erase(test_addr);
    
    // 写入整页数据
    spi_flash_buffer_write(write_buffer, test_addr, SPI_FLASH_PAGE_SIZE);
    
    // 读取数据
    spi_flash_buffer_read(read_buffer, test_addr, SPI_FLASH_PAGE_SIZE);
    
    // 验证数据一致性
    // 可以在此处添加验证逻辑
}

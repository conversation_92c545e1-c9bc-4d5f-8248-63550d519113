/**
 * @file laser_tracking_config.h
 * @brief 激光追踪系统配置文件
 * <AUTHOR> Code
 */

#ifndef __LASER_TRACKING_CONFIG_H__
#define __LASER_TRACKING_CONFIG_H__

/* 系统配置参数 */
#define LASER_TRACKING_VERSION      "v1.0"

/* 追踪性能参数 */
#define TRACK_UPDATE_RATE_MS        20      // 追踪更新频率(ms)
#define TRAJECTORY_BUFFER_SIZE      100     // 轨迹缓冲区大小
#define MAX_TRACKING_SPEED          90      // 最大追踪速度(%)
#define MIN_TRACKING_SPEED          30      // 最小追踪速度(%)

/* 模式切换阈值 */
#define LARGE_ERROR_THRESHOLD       10      // 大误差阈值(启用轨迹插值)
#define SMALL_ERROR_THRESHOLD       3       // 小误差阈值(仅PID控制)
#define TARGET_REACHED_THRESHOLD    1       // 目标到达阈值

/* PID参数建议值(可在运行时调整) */
#define RECOMMENDED_KP_X            1.94f
#define RECOMMENDED_KI_X            0.0f
#define RECOMMENDED_KD_X            0.01f

#define RECOMMENDED_KP_Y            2.2f
#define RECOMMENDED_KI_Y            0.0f
#define RECOMMENDED_KD_Y            0.0f

/* 调试输出控制 */
#define ENABLE_TRACKING_DEBUG       1       // 启用调试输出
#define DEBUG_OUTPUT_INTERVAL       5       // 调试输出间隔(个更新周期)

/* 系统安全参数 */
#define MAX_ERROR_X                 500     // X轴最大允许误差
#define MAX_ERROR_Y                 500     // Y轴最大允许误差
#define TRACKING_TIMEOUT_MS         5000    // 追踪超时时间(ms)

#endif /* __LASER_TRACKING_CONFIG_H__ */
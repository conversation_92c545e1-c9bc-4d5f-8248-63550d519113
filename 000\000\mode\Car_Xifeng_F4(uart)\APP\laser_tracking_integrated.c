/**
 * @file laser_tracking_integrated.c
 * @brief 集成PID控制和轨迹插值的自动激光追踪系统
 * <AUTHOR> Code
 */

#include "MyDefine.h"
#include "StepMotor_app.h"
#include "pid_app.h"
#include <math.h>
#include <stdlib.h>

/* 激光追踪系统配置 */
#define TRACKING_UPDATE_INTERVAL    20      // 追踪更新间隔(ms)
#define TRAJECTORY_ENABLE_THRESHOLD 10      // 启用轨迹插值的误差阈值
#define PID_ONLY_THRESHOLD         3       // 仅使用PID的误差阈值
#define TRACKING_SPEED_BASE        60      // 基础追踪速度百分比
#define TRACKING_SPEED_MAX         90      // 最大追踪速度百分比
#define TRACKING_SPEED_MIN         30      // 最小追踪速度百分比

/* 外部变量引用 */
extern LaserCoord_t latest_red_laser_coord;
extern LaserCoord_t latest_green_laser_coord;
extern PID_T pid_speed_x, pid_speed_y;
extern int error_x, error_y;
extern int output_x, output_y;
extern bool pid_running;

/* 追踪系统状态 */
typedef enum {
    TRACK_IDLE,         // 空闲状态
    TRACK_PID_ONLY,     // 仅PID控制(小误差)
    TRACK_TRAJECTORY,   // 轨迹插值+PID(大误差)
    TRACK_LOST         // 目标丢失
} TrackingMode_t;

/* 追踪系统全局变量 */
static TrackingMode_t current_tracking_mode = TRACK_IDLE;
static uint32_t last_update_time = 0;
static int16_t last_target_x = 0, last_target_y = 0;
static bool system_initialized = false;

/**
 * @brief 初始化激光追踪系统
 */
void LaserTracking_Init(void)
{
    // 初始化步进电机
    StepMotor_Init();
    
    // 初始化PID控制
    PID_Init();
    
    // 初始化轨迹系统
    StepMotor_Trajectory_Init();
    
    // 系统状态初始化
    current_tracking_mode = TRACK_IDLE;
    last_update_time = 0;
    last_target_x = 0;
    last_target_y = 0;
    system_initialized = true;
    
    my_printf(&huart1, "[TRACK] System initialized\r\n");
}

/**
 * @brief 计算追踪速度
 * @param error_magnitude 误差的大小(距离)
 * @return 追踪速度百分比
 */
static uint16_t Calculate_Tracking_Speed(float error_magnitude)
{
    uint16_t speed;
    
    // 根据误差大小动态调整速度
    if (error_magnitude > 100) {
        speed = TRACKING_SPEED_MAX;
    } else if (error_magnitude > 50) {
        speed = TRACKING_SPEED_BASE + 20;
    } else if (error_magnitude > 20) {
        speed = TRACKING_SPEED_BASE;
    } else {
        speed = TRACKING_SPEED_MIN;
    }
    
    return speed;
}

/**
 * @brief 检查激光数据有效性
 * @return true:数据有效；false:数据无效
 */
static bool Check_Laser_Data_Valid(void)
{
    return (latest_red_laser_coord.isValid && latest_green_laser_coord.isValid);
}

/**
 * @brief 计算误差和距离
 * @param error_x_out 输出X轴误差
 * @param error_y_out 输出Y轴误差
 * @param distance_out 输出误差距离
 */
static void Calculate_Error_And_Distance(int *error_x_out, int *error_y_out, float *distance_out)
{
    *error_x_out = latest_green_laser_coord.x - latest_red_laser_coord.x;
    *error_y_out = latest_green_laser_coord.y - latest_red_laser_coord.y;
    *distance_out = sqrt((*error_x_out) * (*error_x_out) + (*error_y_out) * (*error_y_out));
}

/**
 * @brief 轨迹插值模式处理
 * @param target_x 目标X坐标
 * @param target_y 目标Y坐标
 * @param distance 目标距离
 */
static void Handle_Trajectory_Mode(int16_t target_x, int16_t target_y, float distance)
{
    // 计算追踪速度
    uint16_t tracking_speed = Calculate_Tracking_Speed(distance);
    
    // 检查是否需要生成新的轨迹
    bool need_new_trajectory = false;
    
    // 目标位置变化较大时，生成新轨迹
    if (abs(target_x - last_target_x) > 5 || abs(target_y - last_target_y) > 5) {
        need_new_trajectory = true;
    }
    
    // 轨迹执行完成时，检查是否需要更新
    if (StepMotor_Is_Trajectory_Complete()) {
        need_new_trajectory = true;
    }
    
    if (need_new_trajectory) {
        // 清空旧轨迹
        StepMotor_Clear_Trajectory();
        
        // 添加新的追踪轨迹
        if (StepMotor_Move_To_Point(target_x, target_y, tracking_speed)) {
            last_target_x = target_x;
            last_target_y = target_y;
            my_printf(&huart1, "[TRACK] New trajectory: (%d,%d) speed:%d%%\r\n", 
                     target_x, target_y, tracking_speed);
        }
    }
    
    // 执行轨迹
    StepMotor_Trajectory_Execute();
}

/**
 * @brief PID直接控制模式处理
 * @param error_x X轴误差
 * @param error_y Y轴误差
 */
static void Handle_PID_Only_Mode(int error_x, int error_y)
{
    // 计算PID输出
    output_x = pid_calculate_positional(&pid_speed_x, error_x);
    output_y = pid_calculate_positional(&pid_speed_y, error_y);
    
    // 输出限幅
    output_x = pid_constrain(output_x, -100, 100);
    output_y = pid_constrain(output_y, -100, 100);
    
    // 直接控制步进电机
    StepMotor_Set_Speed(output_x, -output_y);
}

/**
 * @brief 选择追踪模式
 * @param distance 当前误差距离
 * @return 选择的追踪模式
 */
static TrackingMode_t Select_Tracking_Mode(float distance)
{
    if (!Check_Laser_Data_Valid()) {
        return TRACK_LOST;
    }
    
    if (distance > TRAJECTORY_ENABLE_THRESHOLD) {
        return TRACK_TRAJECTORY;  // 大误差使用轨迹插值
    } else if (distance > PID_ONLY_THRESHOLD) {
        return TRACK_PID_ONLY;    // 中等误差使用PID
    } else {
        return TRACK_IDLE;        // 小误差或已到达目标
    }
}

/**
 * @brief 激光追踪主任务
 * 集成PID控制和轨迹插值的智能追踪
 */
void LaserTracking_Task(void)
{
    if (!system_initialized) {
        LaserTracking_Init();
        return;
    }
    
    uint32_t current_time = HAL_GetTick();
    
    // 检查更新间隔
    if (current_time - last_update_time < TRACKING_UPDATE_INTERVAL) {
        // 在轨迹模式下，继续执行轨迹
        if (current_tracking_mode == TRACK_TRAJECTORY) {
            StepMotor_Trajectory_Execute();
        }
        return;
    }
    last_update_time = current_time;
    
    // 检查数据有效性
    if (!Check_Laser_Data_Valid()) {
        current_tracking_mode = TRACK_LOST;
        StepMotor_Stop();
        return;
    }
    
    // 计算误差和距离
    int error_x_local, error_y_local;
    float distance;
    Calculate_Error_And_Distance(&error_x_local, &error_y_local, &distance);
    
    // 更新全局误差变量(兼容原有PID代码)
    error_x = error_x_local;
    error_y = error_y_local;
    
    // 选择追踪模式
    TrackingMode_t new_mode = Select_Tracking_Mode(distance);
    
    // 模式切换处理
    if (new_mode != current_tracking_mode) {
        my_printf(&huart1, "[TRACK] Mode switch: %d->%d, dist:%.1f\r\n", 
                 current_tracking_mode, new_mode, distance);
        
        if (new_mode == TRACK_PID_ONLY || new_mode == TRACK_IDLE) {
            // 切换到PID模式时，停止轨迹执行
            StepMotor_Clear_Trajectory();
        }
        
        if (new_mode == TRACK_TRAJECTORY && current_tracking_mode != TRACK_TRAJECTORY) {
            // 切换到轨迹模式时，重置PID
            pid_reset(&pid_speed_x);
            pid_reset(&pid_speed_y);
        }
        
        current_tracking_mode = new_mode;
    }
    
    // 根据模式执行相应的控制策略
    switch (current_tracking_mode) {
        case TRACK_TRAJECTORY:
            // 大误差：使用轨迹插值进行平滑追踪
            Handle_Trajectory_Mode(latest_green_laser_coord.x, latest_green_laser_coord.y, distance);
            break;
            
        case TRACK_PID_ONLY:
            // 中等误差：使用PID直接控制
            Handle_PID_Only_Mode(error_x_local, error_y_local);
            break;
            
        case TRACK_IDLE:
            // 小误差：停止运动或保持微调
            StepMotor_Set_Speed(0, 0);
            break;
            
        case TRACK_LOST:
        default:
            // 目标丢失：停止运动
            StepMotor_Stop();
            break;
    }
    
    // 输出调试信息
    if (distance > 1.0f) {  // 只在有明显误差时输出
        my_printf(&huart1, "[TRACK] Mode:%d, Err:(%d,%d), Dist:%.1f, Out:(%d,%d)\r\n",
                 current_tracking_mode, error_x_local, error_y_local, distance, output_x, output_y);
    }
}

/**
 * @brief 设置追踪系统启用状态
 * @param enable true:启用；false:禁用
 */
void LaserTracking_Enable(bool enable)
{
    pid_running = enable;
    
    if (!enable) {
        StepMotor_Clear_Trajectory();
        StepMotor_Stop();
        current_tracking_mode = TRACK_IDLE;
        my_printf(&huart1, "[TRACK] System disabled\r\n");
    } else {
        my_printf(&huart1, "[TRACK] System enabled\r\n");
    }
}

/**
 * @brief 获取当前追踪模式
 * @return 当前追踪模式
 */
TrackingMode_t LaserTracking_GetMode(void)
{
    return current_tracking_mode;
}

/**
 * @brief 强制切换到指定追踪模式(调试用)
 * @param mode 目标模式
 */
void LaserTracking_ForceMode(TrackingMode_t mode)
{
    current_tracking_mode = mode;
    my_printf(&huart1, "[TRACK] Force mode: %d\r\n", mode);
}
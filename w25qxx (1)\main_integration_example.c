/**
 * @file main_integration_example.c
 * @brief 内部Flash存储集成示例
 * @note 展示如何在main函数中集成内部Flash存储功能
 */

#include "main.h"
#include "Components/backup_storage/internal_flash.h"
#include <stdio.h>
#include <string.h>

/* 系统配置结构 */
typedef struct {
    uint32_t system_mode;       /* 系统工作模式 */
    uint32_t motor_speed;       /* 电机转速 */
    uint32_t sensor_config[4];  /* 传感器配置 */
    uint32_t error_flags;       /* 错误标志 */
    char device_name[16];       /* 设备名称 */
    uint32_t boot_count;        /* 启动计数 */
    uint32_t last_save_time;    /* 最后保存时间 */
} system_config_t;

/* 全局系统配置 */
static system_config_t g_system_config;
static uint8_t config_loaded = 0;

/**
 * @brief 加载系统配置
 * @retval 0-成功, 1-失败
 */
uint8_t load_system_config(void)
{
    printf("正在加载系统配置...\r\n");
    
    /* 初始化Flash存储 */
    if (internal_flash_init() != HAL_OK) {
        printf("Flash存储初始化失败\r\n");
        return 1;
    }
    
    /* 检查是否有有效的保存配置 */
    if (internal_flash_is_data_valid()) {
        printf("检测到有效的保存配置，正在恢复...\r\n");
        
        uint32_t size = sizeof(g_system_config);
        if (internal_flash_read_data((uint8_t*)&g_system_config, &size) == HAL_OK) {
            printf("✓ 系统配置恢复成功:\r\n");
            printf("  - 系统模式: %d\r\n", (int)g_system_config.system_mode);
            printf("  - 电机转速: %d RPM\r\n", (int)g_system_config.motor_speed);
            printf("  - 设备名称: %s\r\n", g_system_config.device_name);
            printf("  - 启动次数: %d\r\n", (int)g_system_config.boot_count);
            
            /* 更新启动次数 */
            g_system_config.boot_count++;
            config_loaded = 1;
            return 0;
        }
    }
    
    /* 没有有效配置，使用默认值 */
    printf("未找到有效配置，使用默认设置\r\n");
    g_system_config.system_mode = 1;
    g_system_config.motor_speed = 1000;
    g_system_config.sensor_config[0] = 1024;
    g_system_config.sensor_config[1] = 2048;
    g_system_config.sensor_config[2] = 512;
    g_system_config.sensor_config[3] = 4096;
    g_system_config.error_flags = 0;
    strcpy(g_system_config.device_name, "STM32F407");
    g_system_config.boot_count = 1;
    g_system_config.last_save_time = HAL_GetTick();
    
    config_loaded = 1;
    return 0;
}

/**
 * @brief 保存系统配置
 * @retval 0-成功, 1-失败
 */
uint8_t save_system_config(void)
{
    if (!config_loaded) {
        printf("配置未加载，无法保存\r\n");
        return 1;
    }
    
    printf("正在保存系统配置...\r\n");
    
    /* 更新保存时间 */
    g_system_config.last_save_time = HAL_GetTick();
    
    /* 写入Flash */
    if (internal_flash_write_data((uint8_t*)&g_system_config, sizeof(g_system_config)) == HAL_OK) {
        printf("✓ 系统配置保存成功\r\n");
        return 0;
    } else {
        printf("✗ 系统配置保存失败\r\n");
        return 1;
    }
}

/**
 * @brief 更新系统配置
 * @param mode 系统模式
 * @param speed 电机转速
 */
void update_system_config(uint32_t mode, uint32_t speed)
{
    if (!config_loaded) {
        printf("配置未加载\r\n");
        return;
    }
    
    printf("更新系统配置: 模式=%d, 转速=%d\r\n", (int)mode, (int)speed);
    
    g_system_config.system_mode = mode;
    g_system_config.motor_speed = speed;
    
    /* 立即保存重要配置更改 */
    save_system_config();
}

/**
 * @brief 获取当前系统配置
 * @retval 系统配置指针
 */
const system_config_t* get_system_config(void)
{
    return config_loaded ? &g_system_config : NULL;
}

/**
 * @brief 系统关键操作前的状态保存
 */
void save_state_before_critical_operation(void)
{
    if (!config_loaded) return;
    
    printf("关键操作前保存状态...\r\n");
    
    /* 设置关键操作标志 */
    g_system_config.error_flags |= 0x8000;
    save_system_config();
}

/**
 * @brief 系统关键操作后的状态更新
 */
void update_state_after_critical_operation(void)
{
    if (!config_loaded) return;
    
    printf("关键操作完成，更新状态...\r\n");
    
    /* 清除关键操作标志 */
    g_system_config.error_flags &= ~0x8000;
    save_system_config();
}

/**
 * @brief 定期保存系统状态 (建议在定时器中调用)
 */
void periodic_save_system_state(void)
{
    static uint32_t last_save_tick = 0;
    uint32_t current_tick = HAL_GetTick();
    
    /* 每30秒保存一次 */
    if (current_tick - last_save_tick > 30000) {
        if (config_loaded) {
            printf("定期保存系统状态...\r\n");
            save_system_config();
            last_save_tick = current_tick;
        }
    }
}

/**
 * @brief 主函数集成示例
 * @note 在您的main.c中参考此代码进行集成
 */
void main_function_example(void)
{
    printf("\r\n========== 系统启动 ==========\r\n");
    
    /* 1. 系统初始化 */
    printf("1. 系统硬件初始化...\r\n");
    // HAL_Init();
    // SystemClock_Config();
    // MX_GPIO_Init();
    // MX_USART1_UART_Init();
    // ... 其他硬件初始化
    
    /* 2. 加载系统配置 */
    printf("2. 加载系统配置...\r\n");
    if (load_system_config() == 0) {
        printf("✓ 系统配置加载成功\r\n");
    } else {
        printf("✗ 系统配置加载失败，使用默认配置\r\n");
    }
    
    /* 3. 检查是否从异常中恢复 */
    const system_config_t* config = get_system_config();
    if (config && (config->error_flags & 0x8000)) {
        printf("⚠ 检测到系统从关键操作中异常重启\r\n");
        printf("正在清除异常标志...\r\n");
        
        /* 清除异常标志并保存 */
        g_system_config.error_flags &= ~0x8000;
        save_system_config();
    }
    
    /* 4. 应用恢复的配置 */
    if (config) {
        printf("4. 应用系统配置:\r\n");
        printf("   - 系统模式: %d\r\n", (int)config->system_mode);
        printf("   - 电机转速: %d RPM\r\n", (int)config->motor_speed);
        
        /* 在这里应用配置到实际硬件 */
        // set_motor_speed(config->motor_speed);
        // configure_sensors(config->sensor_config);
    }
    
    printf("========== 系统启动完成 ==========\r\n\r\n");
    
    /* 5. 主循环示例 */
    printf("进入主循环...\r\n");
    
    uint32_t loop_count = 0;
    while (1) {
        /* 模拟系统运行 */
        HAL_Delay(1000);
        loop_count++;
        
        /* 模拟配置更改 */
        if (loop_count == 5) {
            printf("模拟用户更改配置...\r\n");
            update_system_config(2, 1500);
        }
        
        /* 模拟关键操作 */
        if (loop_count == 10) {
            printf("执行关键操作...\r\n");
            save_state_before_critical_operation();
            
            /* 模拟关键操作 */
            HAL_Delay(500);
            
            update_state_after_critical_operation();
        }
        
        /* 定期保存 */
        periodic_save_system_state();
        
        /* 模拟运行一段时间后退出 */
        if (loop_count >= 20) {
            printf("模拟程序结束，最终保存配置...\r\n");
            save_system_config();
            break;
        }
        
        printf("系统运行中... (循环: %d)\r\n", (int)loop_count);
    }
}

/**
 * @brief 在您的main.c中添加以下代码
 */
/*
// 在main.c的开头添加:
#include "Components/backup_storage/internal_flash.h"

// 在main函数中添加:
int main(void)
{
    // 硬件初始化
    HAL_Init();
    SystemClock_Config();
    MX_GPIO_Init();
    MX_USART1_UART_Init();
    // ... 其他初始化
    
    // 加载系统配置
    load_system_config();
    
    // 主循环
    while (1)
    {
        // 您的应用代码
        
        // 定期保存系统状态
        periodic_save_system_state();
        
        HAL_Delay(100);
    }
}
*/

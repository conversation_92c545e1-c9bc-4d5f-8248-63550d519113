#ifndef FRAM_SPI_H
#define FRAM_SPI_H

#include "main.h"
#include "spi.h"
#include "gpio.h"

/* FRAM (铁电存储器) SPI接口方案 */
/* 支持Cypress/Infineon FM25xxx系列 */

/* 常见FRAM芯片 */
#define FRAM_FM25V01    0   /* 1Kbit (128字节) */
#define FRAM_FM25V02    1   /* 2Kbit (256字节) */
#define FRAM_FM25V05    2   /* 4Kbit (512字节) */
#define FRAM_FM25V10    3   /* 8Kbit (1KB) */
#define FRAM_FM25V20    4   /* 16Kbit (2KB) */
#define FRAM_FM25V40    5   /* 32Kbit (4KB) */
#define FRAM_FM25W256   6   /* 256Kbit (32KB) */
#define FRAM_FM25V16    7   /* 16Mbit (2MB) */

/* 选择使用的FRAM型号 */
#define FRAM_TYPE       FRAM_FM25V40

/* FRAM SPI命令 */
#define FRAM_CMD_WREN   0x06    /* 写使能 */
#define FRAM_CMD_WRDI   0x04    /* 写禁止 */
#define FRAM_CMD_RDSR   0x05    /* 读状态寄存器 */
#define FRAM_CMD_WRSR   0x01    /* 写状态寄存器 */
#define FRAM_CMD_READ   0x03    /* 读数据 */
#define FRAM_CMD_WRITE  0x02    /* 写数据 */
#define FRAM_CMD_RDID   0x9F    /* 读设备ID */

/* FRAM配置 */
#define FRAM_CS_PIN     GPIO_PIN_5      /* 使用PA5作为CS */
#define FRAM_CS_PORT    GPIOA
#define FRAM_CS_LOW()   HAL_GPIO_WritePin(FRAM_CS_PORT, FRAM_CS_PIN, GPIO_PIN_RESET)
#define FRAM_CS_HIGH()  HAL_GPIO_WritePin(FRAM_CS_PORT, FRAM_CS_PIN, GPIO_PIN_SET)

/* SPI句柄 */
extern SPI_HandleTypeDef hspi2;
#define FRAM_SPI_HANDLE &hspi2

/* FRAM容量配置表 */
typedef struct {
    uint32_t size;          /* 容量(字节) */
    uint8_t addr_bytes;     /* 地址字节数 */
    uint32_t device_id;     /* 设备ID */
} fram_config_t;

/* 数据头结构 */
typedef struct {
    uint32_t magic;         /* 魔数 */
    uint32_t version;       /* 版本 */
    uint32_t data_size;     /* 数据大小 */
    uint32_t checksum;      /* 校验和 */
    uint32_t write_count;   /* 写入计数 */
    uint32_t timestamp;     /* 时间戳 */
    uint32_t reserved[2];   /* 保留 */
} fram_header_t;

#define FRAM_MAGIC_VALUE    0x46524D21  /* "FRM!" */
#define FRAM_VERSION        0x0001

/* 函数声明 */
HAL_StatusTypeDef fram_init(void);
HAL_StatusTypeDef fram_read_id(uint32_t* id);
HAL_StatusTypeDef fram_read_status(uint8_t* status);
HAL_StatusTypeDef fram_write_enable(void);
HAL_StatusTypeDef fram_write_disable(void);
HAL_StatusTypeDef fram_read_data(uint32_t addr, uint8_t* data, uint32_t size);
HAL_StatusTypeDef fram_write_data(uint32_t addr, const uint8_t* data, uint32_t size);
HAL_StatusTypeDef fram_write_data_with_header(const uint8_t* data, uint32_t size);
HAL_StatusTypeDef fram_read_data_with_header(uint8_t* data, uint32_t* size);
uint8_t fram_is_data_valid(void);
HAL_StatusTypeDef fram_format(void);

/* 辅助函数 */
uint8_t fram_spi_transfer(uint8_t data);
uint32_t fram_get_capacity(void);
uint32_t fram_calculate_checksum(const uint8_t* data, uint32_t size);

/* 超时定义 */
#define FRAM_TIMEOUT        1000

#endif /* FRAM_SPI_H */

#ifndef _OLED_APP_H_
#define _OLED_APP_H_

#include "MyDefine.h"

/**
 * @brief OLED应用初始化
 * @param 无
 * @return 无
 * @note 初始化OLED硬件，清屏并设置显示参数
 */
void oled_app_init(void);

/**
 * @brief OLED显示任务
 * @param 无  
 * @return 无
 * @note 周期性调用，更新OLED显示内容
 */
void oled_app_task(void);

/**
 * @brief 显示debug数据
 * @param 无
 * @return 无
 * @note 显示debug_yaw和debug_gyro_z数据
 */
void oled_display_debug_data(void);

#endif /* _OLED_APP_H_ */

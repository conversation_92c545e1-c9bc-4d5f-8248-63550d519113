#include "internal_flash.h"
#include <string.h>
#include <stdio.h>

/**
 * @brief 内部Flash存储实现
 * @note 使用STM32F407的最后一个扇区存储用户数据
 */

/* 全局变量 */
static uint8_t flash_initialized = 0;

/**
 * @brief 初始化内部Flash存储
 * @retval HAL状态
 */
HAL_StatusTypeDef internal_flash_init(void)
{
    /* 检查Flash是否已解锁 */
    if (FLASH->CR & FLASH_CR_LOCK) {
        /* Flash被锁定，需要解锁 */
        printf("内部Flash初始化: Flash已锁定，正在解锁...\r\n");
    }
    
    flash_initialized = 1;
    printf("内部Flash初始化完成\r\n");
    printf("数据存储扇区: 0x%08X (扇区11, 128KB)\r\n", USER_DATA_SECTOR);
    
    return HAL_OK;
}

/**
 * @brief 解锁Flash
 * @retval HAL状态
 */
HAL_StatusTypeDef internal_flash_unlock(void)
{
    return HAL_FLASH_Unlock();
}

/**
 * @brief 锁定Flash
 * @retval HAL状态
 */
HAL_StatusTypeDef internal_flash_lock(void)
{
    return HAL_FLASH_Lock();
}

/**
 * @brief 获取地址对应的扇区号
 * @param address Flash地址
 * @retval 扇区号
 */
uint32_t internal_flash_get_sector(uint32_t address)
{
    uint32_t sector = 0;
    
    if (address >= FLASH_SECTOR_11) {
        sector = FLASH_SECTOR_11;
    } else if (address >= FLASH_SECTOR_10) {
        sector = FLASH_SECTOR_10;
    } else if (address >= FLASH_SECTOR_9) {
        sector = FLASH_SECTOR_9;
    } else if (address >= FLASH_SECTOR_8) {
        sector = FLASH_SECTOR_8;
    } else if (address >= FLASH_SECTOR_7) {
        sector = FLASH_SECTOR_7;
    } else if (address >= FLASH_SECTOR_6) {
        sector = FLASH_SECTOR_6;
    } else if (address >= FLASH_SECTOR_5) {
        sector = FLASH_SECTOR_5;
    } else if (address >= FLASH_SECTOR_4) {
        sector = FLASH_SECTOR_4;
    } else if (address >= FLASH_SECTOR_3) {
        sector = FLASH_SECTOR_3;
    } else if (address >= FLASH_SECTOR_2) {
        sector = FLASH_SECTOR_2;
    } else if (address >= FLASH_SECTOR_1) {
        sector = FLASH_SECTOR_1;
    } else {
        sector = FLASH_SECTOR_0;
    }
    
    return sector;
}

/**
 * @brief 擦除用户数据扇区
 * @retval HAL状态
 */
HAL_StatusTypeDef internal_flash_erase_sector(void)
{
    HAL_StatusTypeDef status;
    FLASH_EraseInitTypeDef erase_init;
    uint32_t sector_error = 0;
    
    if (!flash_initialized) {
        return HAL_ERROR;
    }
    
    printf("正在擦除Flash扇区11...\r\n");
    
    /* 解锁Flash */
    status = internal_flash_unlock();
    if (status != HAL_OK) {
        printf("Flash解锁失败\r\n");
        return status;
    }
    
    /* 配置擦除参数 */
    erase_init.TypeErase = FLASH_TYPEERASE_SECTORS;
    erase_init.VoltageRange = FLASH_VOLTAGE_RANGE_3;  /* 2.7V-3.6V */
    erase_init.Sector = FLASH_SECTOR_11;
    erase_init.NbSectors = 1;
    
    /* 执行擦除 */
    status = HAL_FLASHEx_Erase(&erase_init, &sector_error);
    
    /* 锁定Flash */
    internal_flash_lock();
    
    if (status == HAL_OK) {
        printf("Flash扇区擦除成功\r\n");
    } else {
        printf("Flash扇区擦除失败, 错误扇区: %d\r\n", (int)sector_error);
    }
    
    return status;
}

/**
 * @brief 计算数据校验和
 * @param data 数据指针
 * @param size 数据大小
 * @retval 校验和
 */
uint32_t internal_flash_calculate_checksum(const uint8_t* data, uint32_t size)
{
    uint32_t checksum = 0;
    
    for (uint32_t i = 0; i < size; i++) {
        checksum ^= data[i];
        checksum = (checksum << 1) | (checksum >> 31);  /* 循环左移 */
    }
    
    return checksum;
}

/**
 * @brief 写入数据到内部Flash
 * @param data 要写入的数据
 * @param size 数据大小
 * @retval HAL状态
 */
HAL_StatusTypeDef internal_flash_write_data(const uint8_t* data, uint32_t size)
{
    HAL_StatusTypeDef status;
    flash_data_block_t flash_block;
    uint32_t write_address = USER_DATA_SECTOR;
    
    if (!flash_initialized || data == NULL || size == 0) {
        return HAL_ERROR;
    }
    
    if (size > sizeof(flash_block.user_data)) {
        printf("数据大小超出限制: %d > %d\r\n", (int)size, (int)sizeof(flash_block.user_data));
        return HAL_ERROR;
    }
    
    printf("准备写入数据到内部Flash, 大小: %d字节\r\n", (int)size);
    
    /* 准备数据块 */
    memset(&flash_block, 0xFF, sizeof(flash_block));  /* 初始化为0xFF */
    
    /* 填充头部信息 */
    flash_block.header.magic = FLASH_MAGIC_VALUE;
    flash_block.header.version = FLASH_VERSION;
    flash_block.header.data_size = size;
    flash_block.header.timestamp = HAL_GetTick();
    flash_block.header.checksum = internal_flash_calculate_checksum(data, size);
    
    /* 复制用户数据 */
    memcpy(flash_block.user_data, data, size);
    
    /* 擦除扇区 */
    status = internal_flash_erase_sector();
    if (status != HAL_OK) {
        return status;
    }
    
    /* 解锁Flash */
    status = internal_flash_unlock();
    if (status != HAL_OK) {
        return status;
    }
    
    /* 按字(32位)写入数据 */
    uint32_t* src_ptr = (uint32_t*)&flash_block;
    uint32_t words_to_write = (sizeof(flash_block) + 3) / 4;  /* 向上取整 */
    
    printf("开始写入Flash数据...\r\n");
    
    for (uint32_t i = 0; i < words_to_write; i++) {
        status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD, 
                                  write_address + (i * 4), 
                                  src_ptr[i]);
        if (status != HAL_OK) {
            printf("Flash写入失败，地址: 0x%08X\r\n", (unsigned int)(write_address + (i * 4)));
            break;
        }
    }
    
    /* 锁定Flash */
    internal_flash_lock();
    
    if (status == HAL_OK) {
        printf("数据写入Flash成功\r\n");
    } else {
        printf("数据写入Flash失败\r\n");
    }
    
    return status;
}

/**
 * @brief 从内部Flash读取数据
 * @param data 读取数据的缓冲区
 * @param size 缓冲区大小，返回实际读取的大小
 * @retval HAL状态
 */
HAL_StatusTypeDef internal_flash_read_data(uint8_t* data, uint32_t* size)
{
    flash_data_block_t* flash_block = (flash_data_block_t*)USER_DATA_SECTOR;

    if (!flash_initialized || data == NULL || size == NULL) {
        return HAL_ERROR;
    }

    printf("从内部Flash读取数据...\r\n");

    /* 检查数据有效性 */
    if (!internal_flash_is_data_valid()) {
        printf("Flash中无有效数据\r\n");
        *size = 0;
        return HAL_ERROR;
    }

    /* 检查缓冲区大小 */
    if (*size < flash_block->header.data_size) {
        printf("缓冲区太小: %d < %d\r\n", (int)*size, (int)flash_block->header.data_size);
        return HAL_ERROR;
    }

    /* 复制数据 */
    memcpy(data, flash_block->user_data, flash_block->header.data_size);
    *size = flash_block->header.data_size;

    printf("从Flash读取数据成功, 大小: %d字节\r\n", (int)*size);
    printf("数据时间戳: %d\r\n", (int)flash_block->header.timestamp);

    return HAL_OK;
}

/**
 * @brief 检查Flash中的数据是否有效
 * @retval 1-有效, 0-无效
 */
uint8_t internal_flash_is_data_valid(void)
{
    flash_data_block_t* flash_block = (flash_data_block_t*)USER_DATA_SECTOR;

    if (!flash_initialized) {
        return 0;
    }

    /* 检查魔数 */
    if (flash_block->header.magic != FLASH_MAGIC_VALUE) {
        printf("Flash魔数检查失败: 0x%08X != 0x%08X\r\n",
               (unsigned int)flash_block->header.magic,
               (unsigned int)FLASH_MAGIC_VALUE);
        return 0;
    }

    /* 检查版本 */
    if (flash_block->header.version != FLASH_VERSION) {
        printf("Flash版本不匹配: 0x%04X != 0x%04X\r\n",
               flash_block->header.version, FLASH_VERSION);
        return 0;
    }

    /* 检查数据大小 */
    if (flash_block->header.data_size > sizeof(flash_block->user_data)) {
        printf("Flash数据大小异常: %d > %d\r\n",
               (int)flash_block->header.data_size,
               (int)sizeof(flash_block->user_data));
        return 0;
    }

    /* 验证校验和 */
    uint32_t calculated_checksum = internal_flash_calculate_checksum(
        flash_block->user_data,
        flash_block->header.data_size
    );

    if (calculated_checksum != flash_block->header.checksum) {
        printf("Flash校验和验证失败: 0x%08X != 0x%08X\r\n",
               (unsigned int)calculated_checksum,
               (unsigned int)flash_block->header.checksum);
        return 0;
    }

    return 1;
}

/**
 * @brief 内部Flash使用示例
 */
void internal_flash_example_usage(void)
{
    printf("=== 内部Flash存储示例 ===\r\n");

    /* 初始化 */
    if (internal_flash_init() != HAL_OK) {
        printf("内部Flash初始化失败\r\n");
        return;
    }

    /* 准备测试数据 */
    typedef struct {
        uint32_t config_value;
        uint32_t sensor_data[4];
        char device_name[16];
        uint32_t timestamp;
    } test_data_t;

    test_data_t write_data;
    write_data.config_value = 0x12345678;
    write_data.sensor_data[0] = 100;
    write_data.sensor_data[1] = 200;
    write_data.sensor_data[2] = 300;
    write_data.sensor_data[3] = 400;
    strcpy(write_data.device_name, "STM32F407");
    write_data.timestamp = HAL_GetTick();

    /* 写入数据 */
    if (internal_flash_write_data((uint8_t*)&write_data, sizeof(write_data)) == HAL_OK) {
        printf("✓ 数据写入成功\r\n");
    } else {
        printf("✗ 数据写入失败\r\n");
        return;
    }

    /* 模拟系统重启，读取数据 */
    printf("\r\n--- 模拟系统重启 ---\r\n");

    if (internal_flash_is_data_valid()) {
        printf("检测到有效的Flash数据\r\n");

        test_data_t read_data;
        uint32_t read_size = sizeof(read_data);

        if (internal_flash_read_data((uint8_t*)&read_data, &read_size) == HAL_OK) {
            printf("✓ 数据读取成功:\r\n");
            printf("  配置值: 0x%08X\r\n", (unsigned int)read_data.config_value);
            printf("  传感器数据: [%d, %d, %d, %d]\r\n",
                   (int)read_data.sensor_data[0], (int)read_data.sensor_data[1],
                   (int)read_data.sensor_data[2], (int)read_data.sensor_data[3]);
            printf("  设备名称: %s\r\n", read_data.device_name);
            printf("  时间戳: %d\r\n", (int)read_data.timestamp);
        } else {
            printf("✗ 数据读取失败\r\n");
        }
    } else {
        printf("Flash中无有效数据\r\n");
    }

    printf("=== 内部Flash测试完成 ===\r\n");
}

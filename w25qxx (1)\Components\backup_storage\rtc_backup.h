#ifndef RTC_BACKUP_H
#define RTC_BACKUP_H

#include "main.h"
#include "rtc.h"

/* RTC备份寄存器数据保存方案 */
/* STM32F407有20个32位备份寄存器，总共80字节 */

#define BACKUP_REG_COUNT    20
#define BACKUP_DATA_SIZE    (BACKUP_REG_COUNT * 4)  // 80字节

/* 备份寄存器分配 */
#define BACKUP_REG_MAGIC        RTC_BKP_DR0     // 魔数验证
#define BACKUP_REG_VERSION      RTC_BKP_DR1     // 数据版本
#define BACKUP_REG_CHECKSUM     RTC_BKP_DR2     // 校验和
#define BACKUP_REG_DATA_START   RTC_BKP_DR3     // 数据起始位置

#define BACKUP_MAGIC_VALUE      0x12345678      // 魔数
#define BACKUP_VERSION          0x0001          // 版本号

/* 用户数据结构 - 最大68字节 (17个寄存器) */
typedef struct {
    uint32_t system_config;     // 系统配置
    uint32_t user_settings[4];  // 用户设置
    uint32_t sensor_data[8];    // 传感器数据
    uint32_t status_flags;      // 状态标志
    uint32_t timestamp;         // 时间戳
    uint32_t reserved[2];       // 保留字段
} backup_data_t;

/* 函数声明 */
HAL_StatusTypeDef backup_init(void);
HAL_StatusTypeDef backup_write_data(const backup_data_t* data);
HAL_StatusTypeDef backup_read_data(backup_data_t* data);
HAL_StatusTypeDef backup_clear_data(void);
uint8_t backup_is_data_valid(void);
uint32_t backup_calculate_checksum(const backup_data_t* data);

/* 便捷宏定义 */
#define BACKUP_WRITE_REG(reg, value)    HAL_RTCEx_BKUPWrite(&hrtc, reg, value)
#define BACKUP_READ_REG(reg)            HAL_RTCEx_BKUPRead(&hrtc, reg)

#endif /* RTC_BACKUP_H */

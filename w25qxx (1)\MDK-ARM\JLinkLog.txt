T14370 000:006.890   SEGGER J-Link V7.70d Log File
T14370 000:007.255   DLL Compiled: Aug 30 2022 17:10:48
T14370 000:007.276   Logging started @ 2025-07-27 11:11
T14370 000:007.297 - 7.307ms
T14370 000:007.323 JLINK_SetWarnOutHandler(...)
T14370 000:007.346 - 0.032ms
T14370 000:007.368 JLINK_OpenEx(...)
T14370 000:008.678   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T14370 000:009.000   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T14370 000:009.151   Decompressing FW timestamp took 88 us
T14370 000:015.978   Hardware: V9.40
T14370 000:016.021   S/N: 69402406
T14370 000:016.053   OEM: SEGGER
T14370 000:016.084   Feature(s): <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, JFlash
T14370 000:017.114   TELNET listener socket opened on port 19021
T14370 000:017.568   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T14370 000:017.732   WEBSRV Webserver running on local port 19080
T14370 000:031.863 - 24.549ms returns "O.K."
T14370 000:031.990 JLINK_GetEmuCaps()
T14370 000:032.038 - 0.070ms returns 0xB9FF7BBF
T14370 000:032.090 JLINK_TIF_GetAvailable(...)
T14370 000:032.301 - 0.230ms
T14370 000:032.335 JLINK_SetErrorOutHandler(...)
T14370 000:032.356 - 0.031ms
T14370 000:032.394 JLINK_ExecCommand("ProjectFile = "F:\code\Code_Demo\Mcu_Electronics_Studio\MCU.NO2\MainBoard_V2.0\05_W25QXX_f407\w25qxx\MDK-ARM\JLinkSettings.ini"", ...). 
T14370 000:054.592   XML file found at: C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\whxy.xml
T14370 000:054.954   C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\whxy.xml evaluated successfully.
T14370 000:054.983     Device entry created:  CW32F030x6
T14370 000:055.010       ChipInfo:
T14370 000:055.042         Vendor:          WHXY
T14370 000:055.074         Name:            CW32F030x6
T14370 000:055.106         WorkRAMAddr:     0x20000000
T14370 000:055.137         WorkRAMSize:     0x00001800
T14370 000:055.173         Core:            JLINK_CORE_CORTEX_M0
T14370 000:055.205       FlashBankInfo:
T14370 000:055.236         Name:            Flash Block 
T14370 000:055.803         AlwaysPresent:   1
T14370 000:055.839         LoaderInfo:
T14370 000:055.871           Name:            Flash Block 
T14370 000:055.906           MaxSize:         0x00008000
T14370 000:055.938           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\WHXY\CW32F030\FlashCW32F030.FLM
T14370 000:055.969           LoaderType:      FLASH_ALGO_TYPE_OPEN
T14370 000:056.015     Device entry created:  CW32F030x8
T14370 000:056.037       ChipInfo:
T14370 000:056.068         Vendor:          WHXY
T14370 000:056.099         Name:            CW32F030x8
T14370 000:056.131         WorkRAMAddr:     0x20000000
T14370 000:056.162         WorkRAMSize:     0x00002000
T14370 000:056.194         Core:            JLINK_CORE_CORTEX_M0
T14370 000:056.225       FlashBankInfo:
T14370 000:056.256         Name:            Flash Block 
T14370 000:056.799         AlwaysPresent:   1
T14370 000:056.834         LoaderInfo:
T14370 000:056.865           Name:            Flash Block 
T14370 000:056.897           MaxSize:         0x00010000
T14370 000:056.928           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\WHXY\CW32F030\FlashCW32F030.FLM
T14370 000:056.959           LoaderType:      FLASH_ALGO_TYPE_OPEN
T14370 000:056.993     Device entry created:  CW32F003x4
T14370 000:057.015       ChipInfo:
T14370 000:057.046         Vendor:          WHXY
T14370 000:057.077         Name:            CW32F003x4
T14370 000:057.108         WorkRAMAddr:     0x20000000
T14370 000:057.140         WorkRAMSize:     0x00000C00
T14370 000:057.171         Core:            JLINK_CORE_CORTEX_M0
T14370 000:057.202       FlashBankInfo:
T14370 000:057.234         Name:            Flash Block 
T14370 000:057.769         AlwaysPresent:   1
T14370 000:057.808         LoaderInfo:
T14370 000:057.839           Name:            Flash Block 
T14370 000:057.871           MaxSize:         0x00005000
T14370 000:057.902           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\WHXY\CW32F003\FlashCW32F003.FLM
T14370 000:057.963           LoaderType:      FLASH_ALGO_TYPE_OPEN
T14370 000:058.001     Device entry created:  CW32L083xB
T14370 000:058.022       ChipInfo:
T14370 000:058.053         Vendor:          WHXY
T14370 000:058.084         Name:            CW32L083xB
T14370 000:058.116         WorkRAMAddr:     0x20000000
T14370 000:058.147         WorkRAMSize:     0x00006000
T14370 000:058.178         Core:            JLINK_CORE_CORTEX_M0
T14370 000:058.210       FlashBankInfo:
T14370 000:058.241         Name:            Flash Block 
T14370 000:058.795         AlwaysPresent:   1
T14370 000:058.835         LoaderInfo:
T14370 000:058.866           Name:            Flash Block 
T14370 000:058.904           MaxSize:         0x00020000
T14370 000:058.949           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\WHXY\CW32L083\FlashCW32L083.FLM
T14370 000:058.988           LoaderType:      FLASH_ALGO_TYPE_OPEN
T14370 000:059.035     Device entry created:  CW32L083xC
T14370 000:059.061       ChipInfo:
T14370 000:059.099         Vendor:          WHXY
T14370 000:059.138         Name:            CW32L083xC
T14370 000:059.178         WorkRAMAddr:     0x20000000
T14370 000:059.217         WorkRAMSize:     0x00006000
T14370 000:059.262         Core:            JLINK_CORE_CORTEX_M0
T14370 000:059.302       FlashBankInfo:
T14370 000:059.350         Name:            Flash Block 
T14370 000:060.157         AlwaysPresent:   1
T14370 000:060.194         LoaderInfo:
T14370 000:060.226           Name:            Flash Block 
T14370 000:060.257           MaxSize:         0x00040000
T14370 000:060.288           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\WHXY\CW32L083\FlashCW32L083.FLM
T14370 000:060.323           LoaderType:      FLASH_ALGO_TYPE_OPEN
T14370 000:060.358     Device entry created:  CW32L031x8
T14370 000:060.379       ChipInfo:
T14370 000:060.410         Vendor:          WHXY
T14370 000:060.441         Name:            CW32L031x8
T14370 000:060.473         WorkRAMAddr:     0x20000000
T14370 000:060.504         WorkRAMSize:     0x00002000
T14370 000:060.535         Core:            JLINK_CORE_CORTEX_M0
T14370 000:060.567       FlashBankInfo:
T14370 000:060.598         Name:            Flash Block 
T14370 000:061.142         AlwaysPresent:   1
T14370 000:061.177         LoaderInfo:
T14370 000:061.208           Name:            Flash Block 
T14370 000:061.240           MaxSize:         0x00010000
T14370 000:061.271           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\WHXY\CW32L031\FlashCW32L031.FLM
T14370 000:061.303           LoaderType:      FLASH_ALGO_TYPE_OPEN
T14370 000:061.344     Device entry created:  CW32L052x8
T14370 000:061.365       ChipInfo:
T14370 000:061.396         Vendor:          WHXY
T14370 000:061.428         Name:            CW32L052x8
T14370 000:061.459         WorkRAMAddr:     0x20000000
T14370 000:061.491         WorkRAMSize:     0x00002000
T14370 000:061.522         Core:            JLINK_CORE_CORTEX_M0
T14370 000:061.554       FlashBankInfo:
T14370 000:061.601         Name:            Flash Block 
T14370 000:062.201         AlwaysPresent:   1
T14370 000:062.238         LoaderInfo:
T14370 000:062.270           Name:            Flash Block 
T14370 000:062.301           MaxSize:         0x00010000
T14370 000:062.337           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\WHXY\CW32L052\FlashCW32L052.FLM
T14370 000:062.368           LoaderType:      FLASH_ALGO_TYPE_OPEN
T14370 000:062.403     Device entry created:  CW32F020x6
T14370 000:062.424       ChipInfo:
T14370 000:062.455         Vendor:          WHXY
T14370 000:062.486         Name:            CW32F020x6
T14370 000:062.517         WorkRAMAddr:     0x20000000
T14370 000:062.549         WorkRAMSize:     0x00002000
T14370 000:062.580         Core:            JLINK_CORE_CORTEX_M0
T14370 000:062.616       FlashBankInfo:
T14370 000:062.651         Name:            Flash Block 
T14370 000:063.211         AlwaysPresent:   1
T14370 000:063.246         LoaderInfo:
T14370 000:063.277           Name:            Flash Block 
T14370 000:063.308           MaxSize:         0x00008000
T14370 000:063.343           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\WHXY\CW32F020\FlashCW32F020.FLM
T14370 000:063.375           LoaderType:      FLASH_ALGO_TYPE_OPEN
T14370 000:063.412     Device entry created:  CW32F002x3
T14370 000:063.433       ChipInfo:
T14370 000:063.464         Vendor:          WHXY
T14370 000:063.495         Name:            CW32F002x3
T14370 000:063.527         WorkRAMAddr:     0x20000000
T14370 000:063.558         WorkRAMSize:     0x00000800
T14370 000:063.589         Core:            JLINK_CORE_CORTEX_M0
T14370 000:063.621       FlashBankInfo:
T14370 000:063.666         Name:            Flash Block 
T14370 000:064.235         AlwaysPresent:   1
T14370 000:064.271         LoaderInfo:
T14370 000:064.302           Name:            Flash Block 
T14370 000:064.334           MaxSize:         0x00004000
T14370 000:064.369           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\WHXY\CW32F002\FlashCW32F002.FLM
T14370 000:064.400           LoaderType:      FLASH_ALGO_TYPE_OPEN
T14370 000:064.438     Device entry created:  CW32R031x8
T14370 000:064.459       ChipInfo:
T14370 000:064.490         Vendor:          WHXY
T14370 000:064.522         Name:            CW32R031x8
T14370 000:064.553         WorkRAMAddr:     0x20000000
T14370 000:064.585         WorkRAMSize:     0x00002000
T14370 000:064.616         Core:            JLINK_CORE_CORTEX_M0
T14370 000:064.647       FlashBankInfo:
T14370 000:064.679         Name:            Flash Block 
T14370 000:065.190         AlwaysPresent:   1
T14370 000:065.225         LoaderInfo:
T14370 000:065.257           Name:            Flash Block 
T14370 000:065.288           MaxSize:         0x00010000
T14370 000:065.319           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\WHXY\CW32R031\FlashCW32R031.FLM
T14370 000:065.354           LoaderType:      FLASH_ALGO_TYPE_OPEN
T14370 000:065.388     Device entry created:  CW32W031x8
T14370 000:065.409       ChipInfo:
T14370 000:065.440         Vendor:          WHXY
T14370 000:065.472         Name:            CW32W031x8
T14370 000:065.503         WorkRAMAddr:     0x20000000
T14370 000:065.534         WorkRAMSize:     0x00002000
T14370 000:065.566         Core:            JLINK_CORE_CORTEX_M0
T14370 000:065.597       FlashBankInfo:
T14370 000:065.628         Name:            Flash Block 
T14370 000:066.242         AlwaysPresent:   1
T14370 000:066.281         LoaderInfo:
T14370 000:066.313           Name:            Flash Block 
T14370 000:066.345           MaxSize:         0x00010000
T14370 000:066.376           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\WHXY\CW32W031\FlashCW32W031.FLM
T14370 000:066.408           LoaderType:      FLASH_ALGO_TYPE_OPEN
T14370 000:066.444     Device entry created:  CW32L010
T14370 000:066.465       ChipInfo:
T14370 000:066.496         Vendor:          WHXY
T14370 000:066.528         Name:            CW32L010
T14370 000:066.560         Core:            JLINK_CORE_CORTEX_M0
T14370 000:066.592         WorkRAMAddr:     0x20000000
T14370 000:066.623         WorkRAMSize:     0x00001000
T14370 000:066.655       FlashBankInfo:
T14370 000:066.687         Name:            Internal Flash
T14370 000:067.360         AlwaysPresent:   1
T14370 000:067.396         LoaderInfo:
T14370 000:067.427           Name:            Internal Flash
T14370 000:067.458           MaxSize:         0x00010000
T14370 000:067.488           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\WHXY\CW32L010\FlashCW32L010.FLM
T14370 000:067.519           LoaderType:      FLASH_ALGO_TYPE_OPEN
T14370 000:068.321   Ref file found at: D:\Keil\ARM\Segger\JLinkDevices.ref
T14370 000:068.892   REF file references invalid XML file: D:\Jlink\JLinkDevices.xml
T14370 000:069.757 - 37.377ms returns 0x00
T14370 000:071.266 JLINK_ExecCommand("Device = STM32F407VETx", ...). 
T14370 000:072.869   Device "STM32F407VE" selected.
T14370 000:073.624 - 2.341ms returns 0x00
T14370 000:073.653 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T14370 000:073.676 - 0.011ms returns 0x01
T14370 000:073.698 JLINK_GetHardwareVersion()
T14370 000:073.719 - 0.031ms returns 94000
T14370 000:073.748 JLINK_GetDLLVersion()
T14370 000:073.769 - 0.030ms returns 77004
T14370 000:073.790 JLINK_GetOEMString(...)
T14370 000:073.812 JLINK_GetFirmwareString(...)
T14370 000:073.833 - 0.030ms
T14370 000:077.520 JLINK_GetDLLVersion()
T14370 000:077.550 - 0.040ms returns 77004
T14370 000:077.572 JLINK_GetCompileDateTime()
T14370 000:077.592 - 0.030ms
T14370 000:078.487 JLINK_GetFirmwareString(...)
T14370 000:078.516 - 0.039ms
T14370 000:080.697 JLINK_GetHardwareVersion()
T14370 000:080.729 - 0.042ms returns 94000
T14370 000:081.640 JLINK_GetSN()
T14370 000:081.669 - 0.039ms returns 69402406
T14370 000:082.572 JLINK_GetOEMString(...)
T14370 000:084.709 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T14370 000:085.277 - 0.587ms returns 0x00
T14370 000:085.310 JLINK_HasError()
T14370 000:085.347 JLINK_SetSpeed(5000)
T14370 000:085.425 - 0.090ms
T14370 000:085.450 JLINK_GetId()
T14370 000:087.041   InitTarget() start
T14370 000:087.087    J-Link Script File: Executing InitTarget()
T14370 000:093.785   InitTarget() end
T14370 000:100.867   Found SW-DP with ID 0x2BA01477
T14370 000:105.550   DPIDR: 0x2BA01477
T14370 000:108.188   CoreSight SoC-400 or earlier
T14370 000:112.692   Scanning AP map to find all available APs
T14370 000:116.172   AP[1]: Stopped AP scan as end of AP map has been reached
T14370 000:119.882   AP[0]: AHB-AP (IDR: 0x24770011)
T14370 000:122.647   Iterating through AP map to find AHB-AP to use
T14370 000:128.566   AP[0]: Core found
T14370 000:131.346   AP[0]: AHB-AP ROM base: 0xE00FF000
T14370 000:134.332   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T14370 000:136.897   Found Cortex-M4 r0p1, Little endian.
T14370 000:237.348   -- Max. mem block: 0x00010E60
T14370 000:237.513   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T14370 000:237.863   CPU_ReadMem(4 bytes @ 0x********)
T14370 000:241.325   FPUnit: 6 code (BP) slots and 2 literal slots
T14370 000:241.370   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T14370 000:241.731   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T14370 000:242.141   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 000:242.452   CPU_WriteMem(4 bytes @ 0xE0001000)
T14370 000:242.783   CPU_ReadMem(4 bytes @ 0xE000ED88)
T14370 000:243.138   CPU_WriteMem(4 bytes @ 0xE000ED88)
T14370 000:243.435   CPU_ReadMem(4 bytes @ 0xE000ED88)
T14370 000:243.729   CPU_WriteMem(4 bytes @ 0xE000ED88)
T14370 000:246.722   CoreSight components:
T14370 000:250.909   ROMTbl[0] @ E00FF000
T14370 000:250.957   CPU_ReadMem(64 bytes @ 0xE00FF000)
T14370 000:251.660   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T14370 000:255.321   [0][0]: E000E000 CID B105E00D PID 000BB00C SCS-M7
T14370 000:255.407   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T14370 000:258.550   [0][1]: E0001000 CID B105E00D PID 003BB002 DWT
T14370 000:258.594   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T14370 000:261.748   [0][2]: ******** CID B105E00D PID 002BB003 FPB
T14370 000:261.793   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T14370 000:267.592   [0][3]: ******** CID B105E00D PID 003BB001 ITM
T14370 000:267.648   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T14370 000:273.722   [0][4]: ******** CID B105900D PID 000BB9A1 TPIU
T14370 000:273.767   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T14370 000:277.186   [0][5]: ******** CID B105900D PID 000BB925 ETM
T14370 000:277.505 - 192.074ms returns 0x2BA01477
T14370 000:277.548 JLINK_GetDLLVersion()
T14370 000:277.569 - 0.032ms returns 77004
T14370 000:277.594 JLINK_CORE_GetFound()
T14370 000:277.615 - 0.031ms returns 0xE0000FF
T14370 000:277.714 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T14370 000:277.843   Value=0xE00FF000
T14370 000:277.917 - 0.226ms returns 0
T14370 000:283.696 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T14370 000:283.730   Value=0xE00FF000
T14370 000:283.762 - 0.076ms returns 0
T14370 000:283.784 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T14370 000:283.806   Value=0x********
T14370 000:283.837 - 0.063ms returns 0
T14370 000:283.860 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
T14370 000:283.905   CPU_ReadMem(32 bytes @ 0xE0041FD0)
T14370 000:284.381   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T14370 000:284.424 - 0.576ms returns 32 (0x20)
T14370 000:284.450 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T14370 000:284.472   Value=0x00000000
T14370 000:284.504 - 0.064ms returns 0
T14370 000:284.527 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T14370 000:284.548   Value=0x********
T14370 000:284.580 - 0.063ms returns 0
T14370 000:284.603 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T14370 000:284.624   Value=0x********
T14370 000:284.661 - 0.071ms returns 0
T14370 000:284.689 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T14370 000:284.716   Value=0xE0001000
T14370 000:284.755 - 0.076ms returns 0
T14370 000:284.778 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T14370 000:284.800   Value=0x********
T14370 000:284.831 - 0.063ms returns 0
T14370 000:284.854 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T14370 000:284.875   Value=0xE000E000
T14370 000:284.907 - 0.063ms returns 0
T14370 000:284.929 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T14370 000:284.954   Value=0xE000EDF0
T14370 000:284.994 - 0.077ms returns 0
T14370 000:285.019 JLINK_GetDebugInfo(0x01 = Unknown)
T14370 000:285.041   Value=0x00000001
T14370 000:285.073 - 0.064ms returns 0
T14370 000:285.095 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T14370 000:285.121   CPU_ReadMem(4 bytes @ 0xE000ED00)
T14370 000:285.421   Data:  41 C2 0F 41
T14370 000:285.459   Debug reg: CPUID
T14370 000:285.491 - 0.405ms returns 1 (0x1)
T14370 000:285.514 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T14370 000:285.536   Value=0x00000000
T14370 000:285.567 - 0.063ms returns 0
T14370 000:285.590 JLINK_HasError()
T14370 000:285.618 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T14370 000:285.639 - 0.032ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T14370 000:285.661 JLINK_Reset()
T14370 000:285.700   CPU is running
T14370 000:285.732   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T14370 000:286.042   CPU is running
T14370 000:286.078   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T14370 000:289.025   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T14370 000:292.600   Reset: Reset device via AIRCR.SYSRESETREQ.
T14370 000:292.644   CPU is running
T14370 000:292.677   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T14370 000:344.985   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T14370 000:345.341   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T14370 000:345.709   CPU is running
T14370 000:345.785   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T14370 000:346.143   CPU is running
T14370 000:346.216   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T14370 000:352.587   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T14370 000:355.338   CPU_WriteMem(4 bytes @ 0x********)
T14370 000:355.701   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T14370 000:356.836   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 000:357.148 - 71.505ms
T14370 000:357.191 JLINK_Halt()
T14370 000:357.212 - 0.031ms returns 0x00
T14370 000:357.237 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T14370 000:357.264   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T14370 000:357.542   Data:  03 00 03 00
T14370 000:357.574   Debug reg: DHCSR
T14370 000:357.605 - 0.377ms returns 1 (0x1)
T14370 000:357.666 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T14370 000:357.689   Debug reg: DHCSR
T14370 000:357.899   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T14370 000:358.210 - 0.561ms returns 0 (0x00000000)
T14370 000:358.243 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T14370 000:358.266   Debug reg: DEMCR
T14370 000:358.301   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T14370 000:358.607 - 0.390ms returns 0 (0x00000000)
T14370 000:372.177 JLINK_GetHWStatus(...)
T14370 000:372.370 - 0.206ms returns 0
T14370 000:383.197 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T14370 000:383.235 - 0.049ms returns 0x06
T14370 000:383.258 JLINK_GetNumBPUnits(Type = 0xF0)
T14370 000:383.279 - 0.031ms returns 0x2000
T14370 000:383.301 JLINK_GetNumWPUnits()
T14370 000:383.322 - 0.037ms returns 4
T14370 000:393.751 JLINK_GetSpeed()
T14370 000:393.791 - 0.051ms returns 4000
T14370 000:399.757 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T14370 000:399.803   CPU_ReadMem(4 bytes @ 0xE000E004)
T14370 000:400.146   Data:  02 00 00 00
T14370 000:400.181 - 0.434ms returns 1 (0x1)
T14370 000:400.204 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T14370 000:400.227   CPU_ReadMem(4 bytes @ 0xE000E004)
T14370 000:400.505   Data:  02 00 00 00
T14370 000:400.551 - 0.435ms returns 1 (0x1)
T14370 000:400.686 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T14370 000:400.738   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T14370 000:400.823   CPU_WriteMem(28 bytes @ 0xE0001000)
T14370 000:401.323 - 0.657ms returns 0x1C
T14370 000:401.362 JLINK_Halt()
T14370 000:401.384 - 0.032ms returns 0x00
T14370 000:401.406 JLINK_IsHalted()
T14370 000:401.428 - 0.032ms returns TRUE
T14370 000:406.682 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
T14370 000:406.711   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T14370 000:406.918   CPU_WriteMem(388 bytes @ 0x20000000)
T14370 000:409.257 - 2.618ms returns 0x184
T14370 000:409.381 JLINK_HasError()
T14370 000:409.436 JLINK_WriteReg(R0, 0x08000000)
T14370 000:409.487 - 0.075ms returns 0
T14370 000:409.537 JLINK_WriteReg(R1, 0x017D7840)
T14370 000:409.577 - 0.050ms returns 0
T14370 000:409.600 JLINK_WriteReg(R2, 0x00000001)
T14370 000:409.621 - 0.031ms returns 0
T14370 000:409.643 JLINK_WriteReg(R3, 0x00000000)
T14370 000:409.664 - 0.031ms returns 0
T14370 000:409.687 JLINK_WriteReg(R4, 0x00000000)
T14370 000:409.708 - 0.031ms returns 0
T14370 000:409.730 JLINK_WriteReg(R5, 0x00000000)
T14370 000:409.751 - 0.031ms returns 0
T14370 000:409.773 JLINK_WriteReg(R6, 0x00000000)
T14370 000:409.794 - 0.031ms returns 0
T14370 000:409.816 JLINK_WriteReg(R7, 0x00000000)
T14370 000:409.837 - 0.031ms returns 0
T14370 000:409.860 JLINK_WriteReg(R8, 0x00000000)
T14370 000:409.895 - 0.046ms returns 0
T14370 000:409.917 JLINK_WriteReg(R9, 0x20000180)
T14370 000:410.128 - 0.221ms returns 0
T14370 000:410.151 JLINK_WriteReg(R10, 0x00000000)
T14370 000:410.172 - 0.031ms returns 0
T14370 000:410.194 JLINK_WriteReg(R11, 0x00000000)
T14370 000:410.215 - 0.031ms returns 0
T14370 000:410.237 JLINK_WriteReg(R12, 0x00000000)
T14370 000:410.259 - 0.031ms returns 0
T14370 000:410.281 JLINK_WriteReg(R13 (SP), 0x20001000)
T14370 000:410.303 - 0.032ms returns 0
T14370 000:410.325 JLINK_WriteReg(R14, 0x20000001)
T14370 000:410.346 - 0.031ms returns 0
T14370 000:410.368 JLINK_WriteReg(R15 (PC), 0x20000054)
T14370 000:410.395 - 0.037ms returns 0
T14370 000:410.417 JLINK_WriteReg(XPSR, 0x01000000)
T14370 000:410.438 - 0.031ms returns 0
T14370 000:410.461 JLINK_WriteReg(MSP, 0x20001000)
T14370 000:410.482 - 0.031ms returns 0
T14370 000:410.504 JLINK_WriteReg(PSP, 0x20001000)
T14370 000:410.525 - 0.031ms returns 0
T14370 000:410.547 JLINK_WriteReg(CFBP, 0x00000000)
T14370 000:410.573 - 0.035ms returns 0
T14370 000:410.595 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T14370 000:410.622   CPU_ReadMem(2 bytes @ 0x20000000)
T14370 000:410.975 - 0.392ms returns 0x00000001
T14370 000:411.000 JLINK_Go()
T14370 000:411.022   CPU_WriteMem(2 bytes @ 0x20000000)
T14370 000:411.368   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 000:411.674   CPU_WriteMem(4 bytes @ 0xE0002008)
T14370 000:411.707   CPU_WriteMem(4 bytes @ 0xE000200C)
T14370 000:411.738   CPU_WriteMem(4 bytes @ 0xE0002010)
T14370 000:411.772   CPU_WriteMem(4 bytes @ 0xE0002014)
T14370 000:411.803   CPU_WriteMem(4 bytes @ 0xE0002018)
T14370 000:411.834   CPU_WriteMem(4 bytes @ 0xE000201C)
T14370 000:413.167   CPU_WriteMem(4 bytes @ 0xE0001004)
T14370 000:415.953 - 4.972ms
T14370 000:415.986 JLINK_IsHalted()
T14370 000:418.412   CPU_ReadMem(2 bytes @ 0x20000000)
T14370 000:418.723 - 2.749ms returns TRUE
T14370 000:418.748 JLINK_ReadReg(R15 (PC))
T14370 000:418.771 - 0.033ms returns 0x20000000
T14370 000:418.793 JLINK_ClrBPEx(BPHandle = 0x00000001)
T14370 000:418.815 - 0.032ms returns 0x00
T14370 000:418.837 JLINK_ReadReg(R0)
T14370 000:418.858 - 0.031ms returns 0x00000000
T14370 000:421.228 JLINK_HasError()
T14370 000:421.263 JLINK_WriteReg(R0, 0x08000000)
T14370 000:421.287 - 0.034ms returns 0
T14370 000:421.310 JLINK_WriteReg(R1, 0x00004000)
T14370 000:421.332 - 0.032ms returns 0
T14370 000:421.354 JLINK_WriteReg(R2, 0x000000FF)
T14370 000:421.376 - 0.032ms returns 0
T14370 000:421.398 JLINK_WriteReg(R3, 0x00000000)
T14370 000:421.419 - 0.031ms returns 0
T14370 000:421.441 JLINK_WriteReg(R4, 0x00000000)
T14370 000:421.505 - 0.081ms returns 0
T14370 000:421.537 JLINK_WriteReg(R5, 0x00000000)
T14370 000:421.559 - 0.032ms returns 0
T14370 000:421.581 JLINK_WriteReg(R6, 0x00000000)
T14370 000:421.602 - 0.031ms returns 0
T14370 000:421.624 JLINK_WriteReg(R7, 0x00000000)
T14370 000:421.646 - 0.031ms returns 0
T14370 000:421.668 JLINK_WriteReg(R8, 0x00000000)
T14370 000:421.689 - 0.031ms returns 0
T14370 000:421.711 JLINK_WriteReg(R9, 0x20000180)
T14370 000:421.732 - 0.031ms returns 0
T14370 000:421.754 JLINK_WriteReg(R10, 0x00000000)
T14370 000:421.775 - 0.031ms returns 0
T14370 000:421.797 JLINK_WriteReg(R11, 0x00000000)
T14370 000:421.818 - 0.031ms returns 0
T14370 000:421.840 JLINK_WriteReg(R12, 0x00000000)
T14370 000:421.861 - 0.031ms returns 0
T14370 000:421.883 JLINK_WriteReg(R13 (SP), 0x20001000)
T14370 000:421.904 - 0.031ms returns 0
T14370 000:421.926 JLINK_WriteReg(R14, 0x20000001)
T14370 000:421.947 - 0.031ms returns 0
T14370 000:421.969 JLINK_WriteReg(R15 (PC), 0x20000020)
T14370 000:421.990 - 0.031ms returns 0
T14370 000:422.012 JLINK_WriteReg(XPSR, 0x01000000)
T14370 000:422.033 - 0.031ms returns 0
T14370 000:422.055 JLINK_WriteReg(MSP, 0x20001000)
T14370 000:422.076 - 0.031ms returns 0
T14370 000:422.098 JLINK_WriteReg(PSP, 0x20001000)
T14370 000:422.119 - 0.031ms returns 0
T14370 000:422.140 JLINK_WriteReg(CFBP, 0x00000000)
T14370 000:422.161 - 0.031ms returns 0
T14370 000:422.183 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T14370 000:422.205 - 0.037ms returns 0x00000002
T14370 000:422.233 JLINK_Go()
T14370 000:422.259   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 000:425.144 - 2.928ms
T14370 000:425.175 JLINK_IsHalted()
T14370 000:427.699   CPU_ReadMem(2 bytes @ 0x20000000)
T14370 000:428.038 - 2.903ms returns TRUE
T14370 000:428.111 JLINK_ReadReg(R15 (PC))
T14370 000:428.162 - 0.081ms returns 0x20000000
T14370 000:428.219 JLINK_ClrBPEx(BPHandle = 0x00000002)
T14370 000:428.267 - 0.071ms returns 0x00
T14370 000:428.317 JLINK_ReadReg(R0)
T14370 000:428.364 - 0.070ms returns 0x00000001
T14370 000:428.414 JLINK_HasError()
T14370 000:428.464 JLINK_WriteReg(R0, 0x08000000)
T14370 000:428.512 - 0.071ms returns 0
T14370 000:428.561 JLINK_WriteReg(R1, 0x00004000)
T14370 000:428.598 - 0.047ms returns 0
T14370 000:428.620 JLINK_WriteReg(R2, 0x000000FF)
T14370 000:428.641 - 0.031ms returns 0
T14370 000:428.663 JLINK_WriteReg(R3, 0x00000000)
T14370 000:428.684 - 0.031ms returns 0
T14370 000:428.706 JLINK_WriteReg(R4, 0x00000000)
T14370 000:428.727 - 0.031ms returns 0
T14370 000:428.748 JLINK_WriteReg(R5, 0x00000000)
T14370 000:428.769 - 0.031ms returns 0
T14370 000:428.791 JLINK_WriteReg(R6, 0x00000000)
T14370 000:428.816 - 0.035ms returns 0
T14370 000:428.838 JLINK_WriteReg(R7, 0x00000000)
T14370 000:428.859 - 0.031ms returns 0
T14370 000:428.881 JLINK_WriteReg(R8, 0x00000000)
T14370 000:428.902 - 0.031ms returns 0
T14370 000:428.924 JLINK_WriteReg(R9, 0x20000180)
T14370 000:428.950 - 0.036ms returns 0
T14370 000:428.972 JLINK_WriteReg(R10, 0x00000000)
T14370 000:428.993 - 0.031ms returns 0
T14370 000:429.015 JLINK_WriteReg(R11, 0x00000000)
T14370 000:429.035 - 0.031ms returns 0
T14370 000:429.057 JLINK_WriteReg(R12, 0x00000000)
T14370 000:429.078 - 0.031ms returns 0
T14370 000:429.100 JLINK_WriteReg(R13 (SP), 0x20001000)
T14370 000:429.121 - 0.031ms returns 0
T14370 000:429.143 JLINK_WriteReg(R14, 0x20000001)
T14370 000:429.165 - 0.034ms returns 0
T14370 000:429.192 JLINK_WriteReg(R15 (PC), 0x200000C0)
T14370 000:429.244 - 0.062ms returns 0
T14370 000:429.266 JLINK_WriteReg(XPSR, 0x01000000)
T14370 000:429.287 - 0.031ms returns 0
T14370 000:429.309 JLINK_WriteReg(MSP, 0x20001000)
T14370 000:429.330 - 0.031ms returns 0
T14370 000:429.352 JLINK_WriteReg(PSP, 0x20001000)
T14370 000:429.373 - 0.031ms returns 0
T14370 000:429.395 JLINK_WriteReg(CFBP, 0x00000000)
T14370 000:429.416 - 0.031ms returns 0
T14370 000:429.438 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T14370 000:429.566 - 0.139ms returns 0x00000003
T14370 000:429.589 JLINK_Go()
T14370 000:429.617   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 000:432.450 - 2.879ms
T14370 000:432.482 JLINK_IsHalted()
T14370 000:432.778 - 0.307ms returns FALSE
T14370 000:432.801 JLINK_HasError()
T14370 000:448.716 JLINK_IsHalted()
T14370 000:449.153 - 0.480ms returns FALSE
T14370 000:449.227 JLINK_HasError()
T14370 000:450.708 JLINK_IsHalted()
T14370 000:451.064 - 0.380ms returns FALSE
T14370 000:451.114 JLINK_HasError()
T14370 000:452.714 JLINK_IsHalted()
T14370 000:453.056 - 0.367ms returns FALSE
T14370 000:453.102 JLINK_HasError()
T14370 000:454.825 JLINK_IsHalted()
T14370 000:455.153 - 0.347ms returns FALSE
T14370 000:455.186 JLINK_HasError()
T14370 000:456.885 JLINK_IsHalted()
T14370 000:457.257 - 0.411ms returns FALSE
T14370 000:457.326 JLINK_HasError()
T14370 000:458.890 JLINK_IsHalted()
T14370 000:459.264 - 0.412ms returns FALSE
T14370 000:459.332 JLINK_HasError()
T14370 000:460.889 JLINK_IsHalted()
T14370 000:461.266 - 0.416ms returns FALSE
T14370 000:461.334 JLINK_HasError()
T14370 000:462.875 JLINK_IsHalted()
T14370 000:463.224 - 0.389ms returns FALSE
T14370 000:463.295 JLINK_HasError()
T14370 000:464.871 JLINK_IsHalted()
T14370 000:465.167 - 0.317ms returns FALSE
T14370 000:465.202 JLINK_HasError()
T14370 000:469.108 JLINK_IsHalted()
T14370 000:469.478 - 0.421ms returns FALSE
T14370 000:469.562 JLINK_HasError()
T14370 000:471.180 JLINK_IsHalted()
T14370 000:471.554 - 0.414ms returns FALSE
T14370 000:471.626 JLINK_HasError()
T14370 000:473.304 JLINK_IsHalted()
T14370 000:473.643 - 0.361ms returns FALSE
T14370 000:473.684 JLINK_HasError()
T14370 000:475.662 JLINK_IsHalted()
T14370 000:476.024 - 0.381ms returns FALSE
T14370 000:476.058 JLINK_HasError()
T14370 000:477.649 JLINK_IsHalted()
T14370 000:477.992 - 0.381ms returns FALSE
T14370 000:478.060 JLINK_HasError()
T14370 000:479.655 JLINK_IsHalted()
T14370 000:479.977 - 0.363ms returns FALSE
T14370 000:480.050 JLINK_HasError()
T14370 000:482.097 JLINK_IsHalted()
T14370 000:482.425 - 0.346ms returns FALSE
T14370 000:482.457 JLINK_HasError()
T14370 000:484.227 JLINK_IsHalted()
T14370 000:484.599 - 0.399ms returns FALSE
T14370 000:484.654 JLINK_HasError()
T14370 000:486.410 JLINK_IsHalted()
T14370 000:486.710 - 0.317ms returns FALSE
T14370 000:486.740 JLINK_HasError()
T14370 000:488.410 JLINK_IsHalted()
T14370 000:488.873 - 0.476ms returns FALSE
T14370 000:488.900 JLINK_HasError()
T14370 000:490.002 JLINK_IsHalted()
T14370 000:490.369 - 0.395ms returns FALSE
T14370 000:490.426 JLINK_HasError()
T14370 000:492.051 JLINK_IsHalted()
T14370 000:492.381 - 0.350ms returns FALSE
T14370 000:492.415 JLINK_HasError()
T14370 000:494.001 JLINK_IsHalted()
T14370 000:494.342 - 0.359ms returns FALSE
T14370 000:494.373 JLINK_HasError()
T14370 000:495.985 JLINK_IsHalted()
T14370 000:496.264 - 0.298ms returns FALSE
T14370 000:496.298 JLINK_HasError()
T14370 000:497.450 JLINK_IsHalted()
T14370 000:497.789 - 0.359ms returns FALSE
T14370 000:497.823 JLINK_HasError()
T14370 000:499.456 JLINK_IsHalted()
T14370 000:499.804 - 0.370ms returns FALSE
T14370 000:499.840 JLINK_HasError()
T14370 000:501.495 JLINK_IsHalted()
T14370 000:501.788 - 0.311ms returns FALSE
T14370 000:501.820 JLINK_HasError()
T14370 000:503.500 JLINK_IsHalted()
T14370 000:503.844 - 0.355ms returns FALSE
T14370 000:503.869 JLINK_HasError()
T14370 000:505.855 JLINK_IsHalted()
T14370 000:506.232 - 0.395ms returns FALSE
T14370 000:506.265 JLINK_HasError()
T14370 000:507.845 JLINK_IsHalted()
T14370 000:508.184 - 0.378ms returns FALSE
T14370 000:508.254 JLINK_HasError()
T14370 000:509.984 JLINK_IsHalted()
T14370 000:510.278 - 0.311ms returns FALSE
T14370 000:510.310 JLINK_HasError()
T14370 000:511.989 JLINK_IsHalted()
T14370 000:512.328 - 0.358ms returns FALSE
T14370 000:512.361 JLINK_HasError()
T14370 000:513.975 JLINK_IsHalted()
T14370 000:514.281 - 0.324ms returns FALSE
T14370 000:514.314 JLINK_HasError()
T14370 000:515.983 JLINK_IsHalted()
T14370 000:516.282 - 0.314ms returns FALSE
T14370 000:516.311 JLINK_HasError()
T14370 000:517.995 JLINK_IsHalted()
T14370 000:518.345 - 0.375ms returns FALSE
T14370 000:518.399 JLINK_HasError()
T14370 000:519.984 JLINK_IsHalted()
T14370 000:520.282 - 0.316ms returns FALSE
T14370 000:520.314 JLINK_HasError()
T14370 000:521.984 JLINK_IsHalted()
T14370 000:522.286 - 0.314ms returns FALSE
T14370 000:522.311 JLINK_HasError()
T14370 000:523.990 JLINK_IsHalted()
T14370 000:524.337 - 0.371ms returns FALSE
T14370 000:524.388 JLINK_HasError()
T14370 000:525.984 JLINK_IsHalted()
T14370 000:526.278 - 0.313ms returns FALSE
T14370 000:526.312 JLINK_HasError()
T14370 000:528.001 JLINK_IsHalted()
T14370 000:528.356 - 0.392ms returns FALSE
T14370 000:528.423 JLINK_HasError()
T14370 000:529.985 JLINK_IsHalted()
T14370 000:530.434 - 0.462ms returns FALSE
T14370 000:530.460 JLINK_HasError()
T14370 000:531.998 JLINK_IsHalted()
T14370 000:532.337 - 0.358ms returns FALSE
T14370 000:532.370 JLINK_HasError()
T14370 000:534.003 JLINK_IsHalted()
T14370 000:534.391 - 0.413ms returns FALSE
T14370 000:534.442 JLINK_HasError()
T14370 000:536.037 JLINK_IsHalted()
T14370 000:536.437 - 0.422ms returns FALSE
T14370 000:536.486 JLINK_HasError()
T14370 000:538.005 JLINK_IsHalted()
T14370 000:538.345 - 0.359ms returns FALSE
T14370 000:538.378 JLINK_HasError()
T14370 000:539.999 JLINK_IsHalted()
T14370 000:540.360 - 0.400ms returns FALSE
T14370 000:540.428 JLINK_HasError()
T14370 000:541.997 JLINK_IsHalted()
T14370 000:542.339 - 0.361ms returns FALSE
T14370 000:542.373 JLINK_HasError()
T14370 000:545.995 JLINK_IsHalted()
T14370 000:546.366 - 0.410ms returns FALSE
T14370 000:546.435 JLINK_HasError()
T14370 000:547.986 JLINK_IsHalted()
T14370 000:548.293 - 0.323ms returns FALSE
T14370 000:548.323 JLINK_HasError()
T14370 000:549.994 JLINK_IsHalted()
T14370 000:550.352 - 0.381ms returns FALSE
T14370 000:550.401 JLINK_HasError()
T14370 000:551.990 JLINK_IsHalted()
T14370 000:552.480 - 0.529ms returns FALSE
T14370 000:552.550 JLINK_HasError()
T14370 000:553.993 JLINK_IsHalted()
T14370 000:554.342 - 0.372ms returns FALSE
T14370 000:554.390 JLINK_HasError()
T14370 000:555.982 JLINK_IsHalted()
T14370 000:556.285 - 0.322ms returns FALSE
T14370 000:556.318 JLINK_HasError()
T14370 000:557.992 JLINK_IsHalted()
T14370 000:558.353 - 0.385ms returns FALSE
T14370 000:558.404 JLINK_HasError()
T14370 000:560.161 JLINK_IsHalted()
T14370 000:560.457 - 0.315ms returns FALSE
T14370 000:560.490 JLINK_HasError()
T14370 000:562.176 JLINK_IsHalted()
T14370 000:562.475 - 0.316ms returns FALSE
T14370 000:562.507 JLINK_HasError()
T14370 000:564.170 JLINK_IsHalted()
T14370 000:564.465 - 0.313ms returns FALSE
T14370 000:564.498 JLINK_HasError()
T14370 000:566.173 JLINK_IsHalted()
T14370 000:566.455 - 0.293ms returns FALSE
T14370 000:566.479 JLINK_HasError()
T14370 000:568.170 JLINK_IsHalted()
T14370 000:568.472 - 0.320ms returns FALSE
T14370 000:568.505 JLINK_HasError()
T14370 000:570.168 JLINK_IsHalted()
T14370 000:570.457 - 0.307ms returns FALSE
T14370 000:570.490 JLINK_HasError()
T14370 000:572.170 JLINK_IsHalted()
T14370 000:572.469 - 0.317ms returns FALSE
T14370 000:572.502 JLINK_HasError()
T14370 000:576.172 JLINK_IsHalted()
T14370 000:576.487 - 0.334ms returns FALSE
T14370 000:576.521 JLINK_HasError()
T14370 000:578.187 JLINK_IsHalted()
T14370 000:578.544 - 0.388ms returns FALSE
T14370 000:578.604 JLINK_HasError()
T14370 000:580.174 JLINK_IsHalted()
T14370 000:580.476 - 0.320ms returns FALSE
T14370 000:580.509 JLINK_HasError()
T14370 000:582.494 JLINK_IsHalted()
T14370 000:582.861 - 0.405ms returns FALSE
T14370 000:582.929 JLINK_HasError()
T14370 000:584.660 JLINK_IsHalted()
T14370 000:584.956 - 0.315ms returns FALSE
T14370 000:584.989 JLINK_HasError()
T14370 000:586.678 JLINK_IsHalted()
T14370 000:587.031 - 0.381ms returns FALSE
T14370 000:587.090 JLINK_HasError()
T14370 000:588.660 JLINK_IsHalted()
T14370 000:588.954 - 0.306ms returns FALSE
T14370 000:588.980 JLINK_HasError()
T14370 000:590.182 JLINK_IsHalted()
T14370 000:590.539 - 0.395ms returns FALSE
T14370 000:590.608 JLINK_HasError()
T14370 000:592.193 JLINK_IsHalted()
T14370 000:592.567 - 0.413ms returns FALSE
T14370 000:592.636 JLINK_HasError()
T14370 000:594.183 JLINK_IsHalted()
T14370 000:594.578 - 0.437ms returns FALSE
T14370 000:594.650 JLINK_HasError()
T14370 000:596.183 JLINK_IsHalted()
T14370 000:596.543 - 0.398ms returns FALSE
T14370 000:596.611 JLINK_HasError()
T14370 000:598.184 JLINK_IsHalted()
T14370 000:598.558 - 0.414ms returns FALSE
T14370 000:598.629 JLINK_HasError()
T14370 000:602.170 JLINK_IsHalted()
T14370 000:602.506 - 0.354ms returns FALSE
T14370 000:602.539 JLINK_HasError()
T14370 000:604.385 JLINK_IsHalted()
T14370 000:604.691 - 0.325ms returns FALSE
T14370 000:604.725 JLINK_HasError()
T14370 000:606.388 JLINK_IsHalted()
T14370 000:606.709 - 0.332ms returns FALSE
T14370 000:606.733 JLINK_HasError()
T14370 000:608.388 JLINK_IsHalted()
T14370 000:608.732 - 0.384ms returns FALSE
T14370 000:608.804 JLINK_HasError()
T14370 000:610.746 JLINK_IsHalted()
T14370 000:611.164 - 0.458ms returns FALSE
T14370 000:611.235 JLINK_HasError()
T14370 000:612.990 JLINK_IsHalted()
T14370 000:613.342 - 0.371ms returns FALSE
T14370 000:613.375 JLINK_HasError()
T14370 000:615.154 JLINK_IsHalted()
T14370 000:615.523 - 0.387ms returns FALSE
T14370 000:615.559 JLINK_HasError()
T14370 000:617.278 JLINK_IsHalted()
T14370 000:617.776 - 0.516ms returns FALSE
T14370 000:617.808 JLINK_HasError()
T14370 000:619.625 JLINK_IsHalted()
T14370 000:619.965 - 0.359ms returns FALSE
T14370 000:619.998 JLINK_HasError()
T14370 000:621.701 JLINK_IsHalted()
T14370 000:622.147 - 0.465ms returns FALSE
T14370 000:622.180 JLINK_HasError()
T14370 000:623.690 JLINK_IsHalted()
T14370 000:624.034 - 0.362ms returns FALSE
T14370 000:624.067 JLINK_HasError()
T14370 000:625.800 JLINK_IsHalted()
T14370 000:626.149 - 0.361ms returns FALSE
T14370 000:626.175 JLINK_HasError()
T14370 000:627.799 JLINK_IsHalted()
T14370 000:628.157 - 0.396ms returns FALSE
T14370 000:628.226 JLINK_HasError()
T14370 000:629.902 JLINK_IsHalted()
T14370 000:630.208 - 0.318ms returns FALSE
T14370 000:630.233 JLINK_HasError()
T14370 000:631.917 JLINK_IsHalted()
T14370 000:632.256 - 0.358ms returns FALSE
T14370 000:632.290 JLINK_HasError()
T14370 000:634.066 JLINK_IsHalted()
T14370 000:634.371 - 0.324ms returns FALSE
T14370 000:634.404 JLINK_HasError()
T14370 000:636.074 JLINK_IsHalted()
T14370 000:636.417 - 0.361ms returns FALSE
T14370 000:636.449 JLINK_HasError()
T14370 000:638.078 JLINK_IsHalted()
T14370 000:638.402 - 0.362ms returns FALSE
T14370 000:638.471 JLINK_HasError()
T14370 000:640.174 JLINK_IsHalted()
T14370 000:640.536 - 0.401ms returns FALSE
T14370 000:640.604 JLINK_HasError()
T14370 000:642.173 JLINK_IsHalted()
T14370 000:642.587 - 0.453ms returns FALSE
T14370 000:642.655 JLINK_HasError()
T14370 000:644.156 JLINK_IsHalted()
T14370 000:644.508 - 0.389ms returns FALSE
T14370 000:644.580 JLINK_HasError()
T14370 000:646.214 JLINK_IsHalted()
T14370 000:646.538 - 0.342ms returns FALSE
T14370 000:646.570 JLINK_HasError()
T14370 000:648.345 JLINK_IsHalted()
T14370 000:648.710 - 0.478ms returns FALSE
T14370 000:648.838 JLINK_HasError()
T14370 000:650.289 JLINK_IsHalted()
T14370 000:650.587 - 0.316ms returns FALSE
T14370 000:650.619 JLINK_HasError()
T14370 000:651.794 JLINK_IsHalted()
T14370 000:652.148 - 0.372ms returns FALSE
T14370 000:652.180 JLINK_HasError()
T14370 000:653.810 JLINK_IsHalted()
T14370 000:654.156 - 0.363ms returns FALSE
T14370 000:654.188 JLINK_HasError()
T14370 000:656.100 JLINK_IsHalted()
T14370 000:656.435 - 0.375ms returns FALSE
T14370 000:656.507 JLINK_HasError()
T14370 000:658.265 JLINK_IsHalted()
T14370 000:658.568 - 0.321ms returns FALSE
T14370 000:658.601 JLINK_HasError()
T14370 000:660.639 JLINK_IsHalted()
T14370 000:660.929 - 0.308ms returns FALSE
T14370 000:660.961 JLINK_HasError()
T14370 000:662.772 JLINK_IsHalted()
T14370 000:663.188 - 0.436ms returns FALSE
T14370 000:663.240 JLINK_HasError()
T14370 000:665.093 JLINK_IsHalted()
T14370 000:665.431 - 0.357ms returns FALSE
T14370 000:665.465 JLINK_HasError()
T14370 000:667.341 JLINK_IsHalted()
T14370 000:667.634 - 0.305ms returns FALSE
T14370 000:667.659 JLINK_HasError()
T14370 000:670.416 JLINK_IsHalted()
T14370 000:670.705 - 0.301ms returns FALSE
T14370 000:670.730 JLINK_HasError()
T14370 000:672.575 JLINK_IsHalted()
T14370 000:672.947 - 0.392ms returns FALSE
T14370 000:672.981 JLINK_HasError()
T14370 000:674.570 JLINK_IsHalted()
T14370 000:674.864 - 0.313ms returns FALSE
T14370 000:674.897 JLINK_HasError()
T14370 000:676.669 JLINK_IsHalted()
T14370 000:677.031 - 0.388ms returns FALSE
T14370 000:677.072 JLINK_HasError()
T14370 000:678.764 JLINK_IsHalted()
T14370 000:679.150 - 0.404ms returns FALSE
T14370 000:679.183 JLINK_HasError()
T14370 000:683.212 JLINK_IsHalted()
T14370 000:683.520 - 0.326ms returns FALSE
T14370 000:683.553 JLINK_HasError()
T14370 000:685.350 JLINK_IsHalted()
T14370 000:685.726 - 0.402ms returns FALSE
T14370 000:685.780 JLINK_HasError()
T14370 000:687.549 JLINK_IsHalted()
T14370 000:687.853 - 0.324ms returns FALSE
T14370 000:687.888 JLINK_HasError()
T14370 000:690.462 JLINK_IsHalted()
T14370 000:690.915 - 0.480ms returns FALSE
T14370 000:690.971 JLINK_HasError()
T14370 000:692.664 JLINK_IsHalted()
T14370 000:693.023 - 0.377ms returns FALSE
T14370 000:693.055 JLINK_HasError()
T14370 000:694.979 JLINK_IsHalted()
T14370 000:695.273 - 0.312ms returns FALSE
T14370 000:695.306 JLINK_HasError()
T14370 000:697.068 JLINK_IsHalted()
T14370 000:697.371 - 0.321ms returns FALSE
T14370 000:697.403 JLINK_HasError()
T14370 000:700.392 JLINK_IsHalted()
T14370 000:700.720 - 0.370ms returns FALSE
T14370 000:700.794 JLINK_HasError()
T14370 000:702.247 JLINK_IsHalted()
T14370 000:702.571 - 0.363ms returns FALSE
T14370 000:702.642 JLINK_HasError()
T14370 000:704.358 JLINK_IsHalted()
T14370 000:704.674 - 0.359ms returns FALSE
T14370 000:704.750 JLINK_HasError()
T14370 000:706.555 JLINK_IsHalted()
T14370 000:706.896 - 0.380ms returns FALSE
T14370 000:706.967 JLINK_HasError()
T14370 000:708.651 JLINK_IsHalted()
T14370 000:709.041 - 0.409ms returns FALSE
T14370 000:709.075 JLINK_HasError()
T14370 000:710.882 JLINK_IsHalted()
T14370 000:711.206 - 0.343ms returns FALSE
T14370 000:711.239 JLINK_HasError()
T14370 000:712.988 JLINK_IsHalted()
T14370 000:713.347 - 0.378ms returns FALSE
T14370 000:713.381 JLINK_HasError()
T14370 000:715.104 JLINK_IsHalted()
T14370 000:715.446 - 0.360ms returns FALSE
T14370 000:715.478 JLINK_HasError()
T14370 000:717.451 JLINK_IsHalted()
T14370 000:717.764 - 0.332ms returns FALSE
T14370 000:717.797 JLINK_HasError()
T14370 000:720.565 JLINK_IsHalted()
T14370 000:720.858 - 0.311ms returns FALSE
T14370 000:720.891 JLINK_HasError()
T14370 000:722.853 JLINK_IsHalted()
T14370 000:723.160 - 0.329ms returns FALSE
T14370 000:723.196 JLINK_HasError()
T14370 000:725.187 JLINK_IsHalted()
T14370 000:725.475 - 0.311ms returns FALSE
T14370 000:725.515 JLINK_HasError()
T14370 000:727.403 JLINK_IsHalted()
T14370 000:727.704 - 0.314ms returns FALSE
T14370 000:727.731 JLINK_HasError()
T14370 000:729.403 JLINK_IsHalted()
T14370 000:729.885 - 0.521ms returns FALSE
T14370 000:729.956 JLINK_HasError()
T14370 000:731.423 JLINK_IsHalted()
T14370 000:731.768 - 0.364ms returns FALSE
T14370 000:731.801 JLINK_HasError()
T14370 000:733.406 JLINK_IsHalted()
T14370 000:733.710 - 0.322ms returns FALSE
T14370 000:733.743 JLINK_HasError()
T14370 000:735.408 JLINK_IsHalted()
T14370 000:735.736 - 0.339ms returns FALSE
T14370 000:735.761 JLINK_HasError()
T14370 000:737.414 JLINK_IsHalted()
T14370 000:737.777 - 0.389ms returns FALSE
T14370 000:737.831 JLINK_HasError()
T14370 000:739.706 JLINK_IsHalted()
T14370 000:740.006 - 0.312ms returns FALSE
T14370 000:740.037 JLINK_HasError()
T14370 000:741.104 JLINK_IsHalted()
T14370 000:741.379 - 0.292ms returns FALSE
T14370 000:741.411 JLINK_HasError()
T14370 000:743.189 JLINK_IsHalted()
T14370 000:743.484 - 0.307ms returns FALSE
T14370 000:743.509 JLINK_HasError()
T14370 000:745.335 JLINK_IsHalted()
T14370 000:745.660 - 0.363ms returns FALSE
T14370 000:745.730 JLINK_HasError()
T14370 000:747.439 JLINK_IsHalted()
T14370 000:747.729 - 0.302ms returns FALSE
T14370 000:747.753 JLINK_HasError()
T14370 000:749.439 JLINK_IsHalted()
T14370 000:749.737 - 0.317ms returns FALSE
T14370 000:749.769 JLINK_HasError()
T14370 000:751.734 JLINK_IsHalted()
T14370 000:752.034 - 0.312ms returns FALSE
T14370 000:752.059 JLINK_HasError()
T14370 000:753.739 JLINK_IsHalted()
T14370 000:754.093 - 0.374ms returns FALSE
T14370 000:754.127 JLINK_HasError()
T14370 000:755.911 JLINK_IsHalted()
T14370 000:756.213 - 0.321ms returns FALSE
T14370 000:756.247 JLINK_HasError()
T14370 000:757.936 JLINK_IsHalted()
T14370 000:760.430   CPU_ReadMem(2 bytes @ 0x20000000)
T14370 000:760.752 - 2.832ms returns TRUE
T14370 000:760.781 JLINK_ReadReg(R15 (PC))
T14370 000:760.805 - 0.033ms returns 0x20000000
T14370 000:760.828 JLINK_ClrBPEx(BPHandle = 0x00000003)
T14370 000:760.849 - 0.032ms returns 0x00
T14370 000:760.872 JLINK_ReadReg(R0)
T14370 000:760.893 - 0.031ms returns 0x00000000
T14370 000:761.298 JLINK_HasError()
T14370 000:761.329 JLINK_WriteReg(R0, 0x00000001)
T14370 000:761.351 - 0.032ms returns 0
T14370 000:761.373 JLINK_WriteReg(R1, 0x00004000)
T14370 000:761.394 - 0.030ms returns 0
T14370 000:761.415 JLINK_WriteReg(R2, 0x000000FF)
T14370 000:761.435 - 0.030ms returns 0
T14370 000:761.457 JLINK_WriteReg(R3, 0x00000000)
T14370 000:761.477 - 0.030ms returns 0
T14370 000:761.499 JLINK_WriteReg(R4, 0x00000000)
T14370 000:761.519 - 0.030ms returns 0
T14370 000:761.541 JLINK_WriteReg(R5, 0x00000000)
T14370 000:761.561 - 0.030ms returns 0
T14370 000:761.583 JLINK_WriteReg(R6, 0x00000000)
T14370 000:761.603 - 0.030ms returns 0
T14370 000:761.625 JLINK_WriteReg(R7, 0x00000000)
T14370 000:761.645 - 0.030ms returns 0
T14370 000:761.666 JLINK_WriteReg(R8, 0x00000000)
T14370 000:761.687 - 0.030ms returns 0
T14370 000:761.708 JLINK_WriteReg(R9, 0x20000180)
T14370 000:761.729 - 0.030ms returns 0
T14370 000:761.750 JLINK_WriteReg(R10, 0x00000000)
T14370 000:761.770 - 0.030ms returns 0
T14370 000:761.792 JLINK_WriteReg(R11, 0x00000000)
T14370 000:761.812 - 0.030ms returns 0
T14370 000:761.834 JLINK_WriteReg(R12, 0x00000000)
T14370 000:761.854 - 0.030ms returns 0
T14370 000:761.875 JLINK_WriteReg(R13 (SP), 0x20001000)
T14370 000:761.896 - 0.030ms returns 0
T14370 000:761.918 JLINK_WriteReg(R14, 0x20000001)
T14370 000:761.938 - 0.030ms returns 0
T14370 000:761.960 JLINK_WriteReg(R15 (PC), 0x20000086)
T14370 000:761.980 - 0.030ms returns 0
T14370 000:762.002 JLINK_WriteReg(XPSR, 0x01000000)
T14370 000:762.022 - 0.031ms returns 0
T14370 000:762.044 JLINK_WriteReg(MSP, 0x20001000)
T14370 000:762.064 - 0.030ms returns 0
T14370 000:762.086 JLINK_WriteReg(PSP, 0x20001000)
T14370 000:762.106 - 0.030ms returns 0
T14370 000:762.127 JLINK_WriteReg(CFBP, 0x00000000)
T14370 000:762.151 - 0.035ms returns 0
T14370 000:762.174 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T14370 000:762.195 - 0.031ms returns 0x00000004
T14370 000:762.217 JLINK_Go()
T14370 000:762.243   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 000:765.147 - 2.950ms
T14370 000:765.187 JLINK_IsHalted()
T14370 000:767.673   CPU_ReadMem(2 bytes @ 0x20000000)
T14370 000:767.991 - 2.818ms returns TRUE
T14370 000:768.022 JLINK_ReadReg(R15 (PC))
T14370 000:768.045 - 0.033ms returns 0x20000000
T14370 000:768.074 JLINK_ClrBPEx(BPHandle = 0x00000004)
T14370 000:768.096 - 0.032ms returns 0x00
T14370 000:768.120 JLINK_ReadReg(R0)
T14370 000:768.142 - 0.032ms returns 0x00000000
T14370 000:828.203 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
T14370 000:828.243   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T14370 000:828.288   CPU_WriteMem(388 bytes @ 0x20000000)
T14370 000:830.646 - 2.464ms returns 0x184
T14370 000:830.704 JLINK_HasError()
T14370 000:830.728 JLINK_WriteReg(R0, 0x08000000)
T14370 000:830.752 - 0.033ms returns 0
T14370 000:830.774 JLINK_WriteReg(R1, 0x017D7840)
T14370 000:830.795 - 0.031ms returns 0
T14370 000:830.817 JLINK_WriteReg(R2, 0x00000002)
T14370 000:830.838 - 0.031ms returns 0
T14370 000:830.860 JLINK_WriteReg(R3, 0x00000000)
T14370 000:830.881 - 0.031ms returns 0
T14370 000:830.903 JLINK_WriteReg(R4, 0x00000000)
T14370 000:830.924 - 0.031ms returns 0
T14370 000:830.947 JLINK_WriteReg(R5, 0x00000000)
T14370 000:830.968 - 0.031ms returns 0
T14370 000:830.990 JLINK_WriteReg(R6, 0x00000000)
T14370 000:831.011 - 0.031ms returns 0
T14370 000:831.033 JLINK_WriteReg(R7, 0x00000000)
T14370 000:831.054 - 0.031ms returns 0
T14370 000:831.076 JLINK_WriteReg(R8, 0x00000000)
T14370 000:831.097 - 0.031ms returns 0
T14370 000:831.119 JLINK_WriteReg(R9, 0x20000180)
T14370 000:831.140 - 0.031ms returns 0
T14370 000:831.162 JLINK_WriteReg(R10, 0x00000000)
T14370 000:831.183 - 0.031ms returns 0
T14370 000:831.205 JLINK_WriteReg(R11, 0x00000000)
T14370 000:831.226 - 0.031ms returns 0
T14370 000:831.248 JLINK_WriteReg(R12, 0x00000000)
T14370 000:831.269 - 0.031ms returns 0
T14370 000:831.291 JLINK_WriteReg(R13 (SP), 0x20001000)
T14370 000:831.313 - 0.032ms returns 0
T14370 000:831.335 JLINK_WriteReg(R14, 0x20000001)
T14370 000:831.356 - 0.035ms returns 0
T14370 000:831.382 JLINK_WriteReg(R15 (PC), 0x20000054)
T14370 000:831.403 - 0.031ms returns 0
T14370 000:831.425 JLINK_WriteReg(XPSR, 0x01000000)
T14370 000:831.445 - 0.031ms returns 0
T14370 000:831.467 JLINK_WriteReg(MSP, 0x20001000)
T14370 000:831.488 - 0.031ms returns 0
T14370 000:831.510 JLINK_WriteReg(PSP, 0x20001000)
T14370 000:831.530 - 0.031ms returns 0
T14370 000:831.552 JLINK_WriteReg(CFBP, 0x00000000)
T14370 000:831.573 - 0.031ms returns 0
T14370 000:831.594 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T14370 000:831.619   CPU_ReadMem(2 bytes @ 0x20000000)
T14370 000:831.984 - 0.432ms returns 0x00000005
T14370 000:832.059 JLINK_Go()
T14370 000:832.112   CPU_WriteMem(2 bytes @ 0x20000000)
T14370 000:832.489   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 000:835.312 - 3.294ms
T14370 000:835.380 JLINK_IsHalted()
T14370 000:837.935   CPU_ReadMem(2 bytes @ 0x20000000)
T14370 000:838.349 - 2.989ms returns TRUE
T14370 000:838.384 JLINK_ReadReg(R15 (PC))
T14370 000:838.408 - 0.034ms returns 0x20000000
T14370 000:838.430 JLINK_ClrBPEx(BPHandle = 0x00000005)
T14370 000:838.452 - 0.032ms returns 0x00
T14370 000:838.474 JLINK_ReadReg(R0)
T14370 000:838.495 - 0.031ms returns 0x00000000
T14370 000:838.809 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T14370 000:838.964   Data:  B8 04 00 20 9D 01 00 08 23 11 00 08 0D 10 00 08 ...
T14370 000:839.004   CPU_WriteMem(636 bytes @ 0x20000184)
T14370 000:842.494 - 3.705ms returns 0x27C
T14370 000:842.528 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T14370 000:842.549   Data:  00 20 03 46 00 E0 5B 1C 93 42 03 D2 E0 5C CD 5C ...
T14370 000:842.592   CPU_WriteMem(388 bytes @ 0x20000400)
T14370 000:844.905 - 2.397ms returns 0x184
T14370 000:844.949 JLINK_HasError()
T14370 000:844.975 JLINK_WriteReg(R0, 0x08000000)
T14370 000:844.998 - 0.033ms returns 0
T14370 000:845.022 JLINK_WriteReg(R1, 0x00000400)
T14370 000:845.043 - 0.031ms returns 0
T14370 000:845.068 JLINK_WriteReg(R2, 0x20000184)
T14370 000:845.089 - 0.031ms returns 0
T14370 000:845.112 JLINK_WriteReg(R3, 0x00000000)
T14370 000:845.133 - 0.031ms returns 0
T14370 000:845.157 JLINK_WriteReg(R4, 0x00000000)
T14370 000:845.177 - 0.031ms returns 0
T14370 000:845.201 JLINK_WriteReg(R5, 0x00000000)
T14370 000:845.222 - 0.031ms returns 0
T14370 000:845.246 JLINK_WriteReg(R6, 0x00000000)
T14370 000:845.266 - 0.031ms returns 0
T14370 000:845.291 JLINK_WriteReg(R7, 0x00000000)
T14370 000:845.312 - 0.032ms returns 0
T14370 000:845.337 JLINK_WriteReg(R8, 0x00000000)
T14370 000:845.577 - 0.250ms returns 0
T14370 000:845.600 JLINK_WriteReg(R9, 0x20000180)
T14370 000:845.622 - 0.032ms returns 0
T14370 000:845.646 JLINK_WriteReg(R10, 0x00000000)
T14370 000:845.667 - 0.031ms returns 0
T14370 000:845.691 JLINK_WriteReg(R11, 0x00000000)
T14370 000:845.715 - 0.034ms returns 0
T14370 000:845.739 JLINK_WriteReg(R12, 0x00000000)
T14370 000:845.759 - 0.030ms returns 0
T14370 000:845.782 JLINK_WriteReg(R13 (SP), 0x20001000)
T14370 000:845.803 - 0.031ms returns 0
T14370 000:845.826 JLINK_WriteReg(R14, 0x20000001)
T14370 000:845.847 - 0.030ms returns 0
T14370 000:845.870 JLINK_WriteReg(R15 (PC), 0x2000010C)
T14370 000:845.891 - 0.031ms returns 0
T14370 000:845.914 JLINK_WriteReg(XPSR, 0x01000000)
T14370 000:845.935 - 0.030ms returns 0
T14370 000:845.958 JLINK_WriteReg(MSP, 0x20001000)
T14370 000:845.979 - 0.030ms returns 0
T14370 000:846.002 JLINK_WriteReg(PSP, 0x20001000)
T14370 000:846.022 - 0.030ms returns 0
T14370 000:846.046 JLINK_WriteReg(CFBP, 0x00000000)
T14370 000:846.066 - 0.030ms returns 0
T14370 000:846.089 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T14370 000:846.110 - 0.133ms returns 0x00000006
T14370 000:846.238 JLINK_Go()
T14370 000:846.264   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 000:849.037 - 2.818ms
T14370 000:849.076 JLINK_IsHalted()
T14370 000:849.353 - 0.296ms returns FALSE
T14370 000:849.391 JLINK_HasError()
T14370 000:858.968 JLINK_IsHalted()
T14370 000:861.547   CPU_ReadMem(2 bytes @ 0x20000000)
T14370 000:861.835 - 2.879ms returns TRUE
T14370 000:861.860 JLINK_ReadReg(R15 (PC))
T14370 000:861.883 - 0.033ms returns 0x20000000
T14370 000:861.905 JLINK_ClrBPEx(BPHandle = 0x00000006)
T14370 000:861.927 - 0.031ms returns 0x00
T14370 000:861.949 JLINK_ReadReg(R0)
T14370 000:861.970 - 0.031ms returns 0x00000000
T14370 000:862.418 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T14370 000:862.447   Data:  02 D1 00 27 24 E0 54 E0 31 4F B8 42 01 D1 01 27 ...
T14370 000:862.484   CPU_WriteMem(636 bytes @ 0x20000184)
T14370 000:865.942 - 3.544ms returns 0x27C
T14370 000:865.976 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T14370 000:865.998   Data:  30 49 68 68 08 39 01 28 12 D0 02 28 13 D0 03 28 ...
T14370 000:866.033   CPU_WriteMem(388 bytes @ 0x20000400)
T14370 000:868.356 - 2.399ms returns 0x184
T14370 000:868.390 JLINK_HasError()
T14370 000:868.413 JLINK_WriteReg(R0, 0x08000400)
T14370 000:868.436 - 0.033ms returns 0
T14370 000:868.459 JLINK_WriteReg(R1, 0x00000400)
T14370 000:868.480 - 0.031ms returns 0
T14370 000:868.502 JLINK_WriteReg(R2, 0x20000184)
T14370 000:868.523 - 0.031ms returns 0
T14370 000:868.545 JLINK_WriteReg(R3, 0x00000000)
T14370 000:868.566 - 0.031ms returns 0
T14370 000:868.588 JLINK_WriteReg(R4, 0x00000000)
T14370 000:868.609 - 0.031ms returns 0
T14370 000:868.631 JLINK_WriteReg(R5, 0x00000000)
T14370 000:868.652 - 0.031ms returns 0
T14370 000:868.674 JLINK_WriteReg(R6, 0x00000000)
T14370 000:868.695 - 0.031ms returns 0
T14370 000:868.717 JLINK_WriteReg(R7, 0x00000000)
T14370 000:868.738 - 0.031ms returns 0
T14370 000:868.760 JLINK_WriteReg(R8, 0x00000000)
T14370 000:868.781 - 0.031ms returns 0
T14370 000:868.803 JLINK_WriteReg(R9, 0x20000180)
T14370 000:868.824 - 0.031ms returns 0
T14370 000:868.850 JLINK_WriteReg(R10, 0x00000000)
T14370 000:868.873 - 0.033ms returns 0
T14370 000:868.894 JLINK_WriteReg(R11, 0x00000000)
T14370 000:868.915 - 0.031ms returns 0
T14370 000:868.937 JLINK_WriteReg(R12, 0x00000000)
T14370 000:868.958 - 0.031ms returns 0
T14370 000:868.980 JLINK_WriteReg(R13 (SP), 0x20001000)
T14370 000:869.001 - 0.031ms returns 0
T14370 000:869.023 JLINK_WriteReg(R14, 0x20000001)
T14370 000:869.044 - 0.031ms returns 0
T14370 000:869.066 JLINK_WriteReg(R15 (PC), 0x2000010C)
T14370 000:869.087 - 0.031ms returns 0
T14370 000:869.109 JLINK_WriteReg(XPSR, 0x01000000)
T14370 000:869.131 - 0.031ms returns 0
T14370 000:869.152 JLINK_WriteReg(MSP, 0x20001000)
T14370 000:869.173 - 0.031ms returns 0
T14370 000:869.196 JLINK_WriteReg(PSP, 0x20001000)
T14370 000:869.216 - 0.031ms returns 0
T14370 000:869.238 JLINK_WriteReg(CFBP, 0x00000000)
T14370 000:869.265 - 0.036ms returns 0
T14370 000:869.287 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T14370 000:869.309 - 0.032ms returns 0x00000007
T14370 000:869.331 JLINK_Go()
T14370 000:869.356   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 000:872.152 - 2.840ms
T14370 000:872.185 JLINK_IsHalted()
T14370 000:872.465 - 0.297ms returns FALSE
T14370 000:872.497 JLINK_HasError()
T14370 000:874.431 JLINK_IsHalted()
T14370 000:874.733 - 0.313ms returns FALSE
T14370 000:874.757 JLINK_HasError()
T14370 000:876.471 JLINK_IsHalted()
T14370 000:878.983   CPU_ReadMem(2 bytes @ 0x20000000)
T14370 000:879.333 - 2.873ms returns TRUE
T14370 000:879.357 JLINK_ReadReg(R15 (PC))
T14370 000:879.379 - 0.032ms returns 0x20000000
T14370 000:879.401 JLINK_ClrBPEx(BPHandle = 0x00000007)
T14370 000:879.421 - 0.030ms returns 0x00
T14370 000:879.443 JLINK_ReadReg(R0)
T14370 000:879.468 - 0.035ms returns 0x00000000
T14370 000:881.240 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T14370 000:881.271   Data:  4C 93 C0 07 A9 F1 04 05 4A D0 09 F1 04 00 01 68 ...
T14370 000:881.308   CPU_WriteMem(636 bytes @ 0x20000184)
T14370 000:884.768 - 3.549ms returns 0x27C
T14370 000:884.806 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T14370 000:884.832   Data:  60 00 FF F7 3B FD 06 46 05 E0 00 BF FF F7 36 FD ...
T14370 000:884.885   CPU_WriteMem(388 bytes @ 0x20000400)
T14370 000:887.195 - 2.408ms returns 0x184
T14370 000:887.228 JLINK_HasError()
T14370 000:887.251 JLINK_WriteReg(R0, 0x08000800)
T14370 000:887.273 - 0.033ms returns 0
T14370 000:887.295 JLINK_WriteReg(R1, 0x00000400)
T14370 000:887.316 - 0.031ms returns 0
T14370 000:887.338 JLINK_WriteReg(R2, 0x20000184)
T14370 000:887.359 - 0.031ms returns 0
T14370 000:887.380 JLINK_WriteReg(R3, 0x00000000)
T14370 000:887.401 - 0.030ms returns 0
T14370 000:887.423 JLINK_WriteReg(R4, 0x00000000)
T14370 000:887.444 - 0.031ms returns 0
T14370 000:887.465 JLINK_WriteReg(R5, 0x00000000)
T14370 000:887.489 - 0.034ms returns 0
T14370 000:887.511 JLINK_WriteReg(R6, 0x00000000)
T14370 000:887.531 - 0.030ms returns 0
T14370 000:887.552 JLINK_WriteReg(R7, 0x00000000)
T14370 000:887.572 - 0.030ms returns 0
T14370 000:887.594 JLINK_WriteReg(R8, 0x00000000)
T14370 000:887.614 - 0.030ms returns 0
T14370 000:887.635 JLINK_WriteReg(R9, 0x20000180)
T14370 000:887.655 - 0.030ms returns 0
T14370 000:887.676 JLINK_WriteReg(R10, 0x00000000)
T14370 000:887.697 - 0.030ms returns 0
T14370 000:887.718 JLINK_WriteReg(R11, 0x00000000)
T14370 000:887.738 - 0.030ms returns 0
T14370 000:887.759 JLINK_WriteReg(R12, 0x00000000)
T14370 000:887.780 - 0.030ms returns 0
T14370 000:887.801 JLINK_WriteReg(R13 (SP), 0x20001000)
T14370 000:887.822 - 0.030ms returns 0
T14370 000:887.843 JLINK_WriteReg(R14, 0x20000001)
T14370 000:887.863 - 0.030ms returns 0
T14370 000:887.884 JLINK_WriteReg(R15 (PC), 0x2000010C)
T14370 000:887.905 - 0.030ms returns 0
T14370 000:887.927 JLINK_WriteReg(XPSR, 0x01000000)
T14370 000:887.947 - 0.031ms returns 0
T14370 000:887.969 JLINK_WriteReg(MSP, 0x20001000)
T14370 000:887.990 - 0.030ms returns 0
T14370 000:888.011 JLINK_WriteReg(PSP, 0x20001000)
T14370 000:888.032 - 0.030ms returns 0
T14370 000:888.058 JLINK_WriteReg(CFBP, 0x00000000)
T14370 000:888.081 - 0.032ms returns 0
T14370 000:888.103 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T14370 000:888.124 - 0.031ms returns 0x00000008
T14370 000:888.146 JLINK_Go()
T14370 000:888.170   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 000:891.005 - 2.899ms
T14370 000:891.076 JLINK_IsHalted()
T14370 000:891.409 - 0.351ms returns FALSE
T14370 000:891.441 JLINK_HasError()
T14370 000:894.172 JLINK_IsHalted()
T14370 000:894.544 - 0.393ms returns FALSE
T14370 000:894.580 JLINK_HasError()
T14370 000:897.182 JLINK_IsHalted()
T14370 000:899.692   CPU_ReadMem(2 bytes @ 0x20000000)
T14370 000:899.982 - 2.811ms returns TRUE
T14370 000:900.007 JLINK_ReadReg(R15 (PC))
T14370 000:900.029 - 0.032ms returns 0x20000000
T14370 000:900.051 JLINK_ClrBPEx(BPHandle = 0x00000008)
T14370 000:900.072 - 0.031ms returns 0x00
T14370 000:900.094 JLINK_ReadReg(R0)
T14370 000:900.115 - 0.031ms returns 0x00000000
T14370 000:900.658 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T14370 000:900.690   Data:  08 43 21 68 48 60 20 68 C1 69 21 F4 00 61 C1 61 ...
T14370 000:900.726   CPU_WriteMem(636 bytes @ 0x20000184)
T14370 000:904.171 - 3.530ms returns 0x27C
T14370 000:904.203 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T14370 000:904.224   Data:  01 D3 01 20 10 BD 4F F0 E0 24 60 61 0F 21 60 17 ...
T14370 000:904.258   CPU_WriteMem(388 bytes @ 0x20000400)
T14370 000:906.605 - 2.442ms returns 0x184
T14370 000:906.675 JLINK_HasError()
T14370 000:906.725 JLINK_WriteReg(R0, 0x08000C00)
T14370 000:906.774 - 0.072ms returns 0
T14370 000:906.822 JLINK_WriteReg(R1, 0x00000400)
T14370 000:906.868 - 0.068ms returns 0
T14370 000:906.916 JLINK_WriteReg(R2, 0x20000184)
T14370 000:906.961 - 0.068ms returns 0
T14370 000:907.009 JLINK_WriteReg(R3, 0x00000000)
T14370 000:907.055 - 0.068ms returns 0
T14370 000:907.103 JLINK_WriteReg(R4, 0x00000000)
T14370 000:907.148 - 0.068ms returns 0
T14370 000:907.196 JLINK_WriteReg(R5, 0x00000000)
T14370 000:907.242 - 0.068ms returns 0
T14370 000:907.289 JLINK_WriteReg(R6, 0x00000000)
T14370 000:907.335 - 0.068ms returns 0
T14370 000:907.383 JLINK_WriteReg(R7, 0x00000000)
T14370 000:907.428 - 0.068ms returns 0
T14370 000:907.476 JLINK_WriteReg(R8, 0x00000000)
T14370 000:907.526 - 0.062ms returns 0
T14370 000:907.550 JLINK_WriteReg(R9, 0x20000180)
T14370 000:907.570 - 0.030ms returns 0
T14370 000:907.594 JLINK_WriteReg(R10, 0x00000000)
T14370 000:907.615 - 0.030ms returns 0
T14370 000:907.636 JLINK_WriteReg(R11, 0x00000000)
T14370 000:907.657 - 0.030ms returns 0
T14370 000:907.678 JLINK_WriteReg(R12, 0x00000000)
T14370 000:907.699 - 0.030ms returns 0
T14370 000:907.720 JLINK_WriteReg(R13 (SP), 0x20001000)
T14370 000:907.741 - 0.031ms returns 0
T14370 000:907.762 JLINK_WriteReg(R14, 0x20000001)
T14370 000:907.782 - 0.030ms returns 0
T14370 000:907.804 JLINK_WriteReg(R15 (PC), 0x2000010C)
T14370 000:907.824 - 0.030ms returns 0
T14370 000:907.846 JLINK_WriteReg(XPSR, 0x01000000)
T14370 000:907.866 - 0.030ms returns 0
T14370 000:907.888 JLINK_WriteReg(MSP, 0x20001000)
T14370 000:907.908 - 0.030ms returns 0
T14370 000:907.929 JLINK_WriteReg(PSP, 0x20001000)
T14370 000:907.950 - 0.030ms returns 0
T14370 000:907.971 JLINK_WriteReg(CFBP, 0x00000000)
T14370 000:907.991 - 0.030ms returns 0
T14370 000:908.013 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T14370 000:908.034 - 0.031ms returns 0x00000009
T14370 000:908.055 JLINK_Go()
T14370 000:908.080   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 000:910.824 - 2.990ms
T14370 000:911.061 JLINK_IsHalted()
T14370 000:911.364 - 0.323ms returns FALSE
T14370 000:911.399 JLINK_HasError()
T14370 000:912.852 JLINK_IsHalted()
T14370 000:913.241 - 0.430ms returns FALSE
T14370 000:913.314 JLINK_HasError()
T14370 000:914.953 JLINK_IsHalted()
T14370 000:917.457   CPU_ReadMem(2 bytes @ 0x20000000)
T14370 000:917.756 - 2.821ms returns TRUE
T14370 000:917.789 JLINK_ReadReg(R15 (PC))
T14370 000:918.072 - 0.293ms returns 0x20000000
T14370 000:918.094 JLINK_ClrBPEx(BPHandle = 0x00000009)
T14370 000:918.147 - 0.064ms returns 0x00
T14370 000:918.175 JLINK_ReadReg(R0)
T14370 000:918.196 - 0.031ms returns 0x00000000
T14370 000:918.775 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T14370 000:918.806   Data:  08 00 E3 E7 84 F8 41 70 00 20 BA E7 FE E7 00 00 ...
T14370 000:918.842   CPU_WriteMem(636 bytes @ 0x20000184)
T14370 000:922.315 - 3.559ms returns 0x27C
T14370 000:922.349 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T14370 000:922.370   Data:  80 50 11 90 19 48 01 68 41 F4 80 41 01 60 00 68 ...
T14370 000:922.405   CPU_WriteMem(388 bytes @ 0x20000400)
T14370 000:924.742 - 2.431ms returns 0x184
T14370 000:924.809 JLINK_HasError()
T14370 000:924.856 JLINK_WriteReg(R0, 0x08001000)
T14370 000:924.902 - 0.067ms returns 0
T14370 000:924.947 JLINK_WriteReg(R1, 0x00000400)
T14370 000:924.990 - 0.064ms returns 0
T14370 000:925.035 JLINK_WriteReg(R2, 0x20000184)
T14370 000:925.077 - 0.064ms returns 0
T14370 000:925.122 JLINK_WriteReg(R3, 0x00000000)
T14370 000:925.165 - 0.063ms returns 0
T14370 000:925.209 JLINK_WriteReg(R4, 0x00000000)
T14370 000:925.252 - 0.064ms returns 0
T14370 000:925.297 JLINK_WriteReg(R5, 0x00000000)
T14370 000:925.339 - 0.063ms returns 0
T14370 000:925.384 JLINK_WriteReg(R6, 0x00000000)
T14370 000:925.426 - 0.063ms returns 0
T14370 000:925.471 JLINK_WriteReg(R7, 0x00000000)
T14370 000:925.513 - 0.074ms returns 0
T14370 000:925.569 JLINK_WriteReg(R8, 0x00000000)
T14370 000:925.611 - 0.065ms returns 0
T14370 000:925.677 JLINK_WriteReg(R9, 0x20000180)
T14370 000:925.704 - 0.037ms returns 0
T14370 000:925.726 JLINK_WriteReg(R10, 0x00000000)
T14370 000:925.749 - 0.036ms returns 0
T14370 000:925.778 JLINK_WriteReg(R11, 0x00000000)
T14370 000:925.801 - 0.034ms returns 0
T14370 000:925.828 JLINK_WriteReg(R12, 0x00000000)
T14370 000:925.851 - 0.032ms returns 0
T14370 000:925.875 JLINK_WriteReg(R13 (SP), 0x20001000)
T14370 000:925.896 - 0.031ms returns 0
T14370 000:925.920 JLINK_WriteReg(R14, 0x20000001)
T14370 000:925.941 - 0.031ms returns 0
T14370 000:925.965 JLINK_WriteReg(R15 (PC), 0x2000010C)
T14370 000:925.986 - 0.031ms returns 0
T14370 000:926.010 JLINK_WriteReg(XPSR, 0x01000000)
T14370 000:926.031 - 0.031ms returns 0
T14370 000:926.054 JLINK_WriteReg(MSP, 0x20001000)
T14370 000:926.075 - 0.031ms returns 0
T14370 000:926.099 JLINK_WriteReg(PSP, 0x20001000)
T14370 000:926.120 - 0.031ms returns 0
T14370 000:926.144 JLINK_WriteReg(CFBP, 0x00000000)
T14370 000:926.164 - 0.031ms returns 0
T14370 000:926.188 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T14370 000:926.210 - 0.032ms returns 0x0000000A
T14370 000:926.234 JLINK_Go()
T14370 000:926.259   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 000:929.058 - 2.864ms
T14370 000:929.136 JLINK_IsHalted()
T14370 000:929.480 - 0.383ms returns FALSE
T14370 000:929.556 JLINK_HasError()
T14370 000:930.940 JLINK_IsHalted()
T14370 000:931.232 - 0.303ms returns FALSE
T14370 000:931.256 JLINK_HasError()
T14370 000:933.048 JLINK_IsHalted()
T14370 000:933.345 - 0.315ms returns FALSE
T14370 000:933.378 JLINK_HasError()
T14370 000:935.144 JLINK_IsHalted()
T14370 000:937.630   CPU_ReadMem(2 bytes @ 0x20000000)
T14370 000:938.003 - 2.900ms returns TRUE
T14370 000:938.075 JLINK_ReadReg(R15 (PC))
T14370 000:938.121 - 0.068ms returns 0x20000000
T14370 000:938.168 JLINK_ClrBPEx(BPHandle = 0x0000000A)
T14370 000:938.211 - 0.064ms returns 0x00
T14370 000:938.256 JLINK_ReadReg(R0)
T14370 000:938.299 - 0.064ms returns 0x00000000
T14370 000:939.020 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T14370 000:939.052   Data:  88 60 70 BD A0 FB 01 01 92 18 5B 41 FE F7 D8 FE ...
T14370 000:939.088   CPU_WriteMem(636 bytes @ 0x20000184)
T14370 000:942.541 - 3.539ms returns 0x27C
T14370 000:942.573 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T14370 000:942.599   Data:  0D D0 01 CF C1 17 03 1E 71 F1 00 03 0D DA 4F F0 ...
T14370 000:942.634   CPU_WriteMem(388 bytes @ 0x20000400)
T14370 000:944.947 - 2.392ms returns 0x184
T14370 000:944.980 JLINK_HasError()
T14370 000:945.002 JLINK_WriteReg(R0, 0x08001400)
T14370 000:945.029 - 0.037ms returns 0
T14370 000:945.051 JLINK_WriteReg(R1, 0x00000400)
T14370 000:945.071 - 0.030ms returns 0
T14370 000:945.092 JLINK_WriteReg(R2, 0x20000184)
T14370 000:945.112 - 0.030ms returns 0
T14370 000:945.133 JLINK_WriteReg(R3, 0x00000000)
T14370 000:945.153 - 0.030ms returns 0
T14370 000:945.174 JLINK_WriteReg(R4, 0x00000000)
T14370 000:945.194 - 0.030ms returns 0
T14370 000:945.216 JLINK_WriteReg(R5, 0x00000000)
T14370 000:945.236 - 0.030ms returns 0
T14370 000:945.257 JLINK_WriteReg(R6, 0x00000000)
T14370 000:945.277 - 0.030ms returns 0
T14370 000:945.298 JLINK_WriteReg(R7, 0x00000000)
T14370 000:945.318 - 0.030ms returns 0
T14370 000:945.339 JLINK_WriteReg(R8, 0x00000000)
T14370 000:945.359 - 0.030ms returns 0
T14370 000:945.380 JLINK_WriteReg(R9, 0x20000180)
T14370 000:945.401 - 0.034ms returns 0
T14370 000:945.426 JLINK_WriteReg(R10, 0x00000000)
T14370 000:945.446 - 0.030ms returns 0
T14370 000:945.467 JLINK_WriteReg(R11, 0x00000000)
T14370 000:945.487 - 0.030ms returns 0
T14370 000:945.508 JLINK_WriteReg(R12, 0x00000000)
T14370 000:945.528 - 0.030ms returns 0
T14370 000:945.549 JLINK_WriteReg(R13 (SP), 0x20001000)
T14370 000:945.570 - 0.030ms returns 0
T14370 000:945.595 JLINK_WriteReg(R14, 0x20000001)
T14370 000:945.615 - 0.030ms returns 0
T14370 000:945.637 JLINK_WriteReg(R15 (PC), 0x2000010C)
T14370 000:945.657 - 0.030ms returns 0
T14370 000:945.679 JLINK_WriteReg(XPSR, 0x01000000)
T14370 000:945.699 - 0.030ms returns 0
T14370 000:945.721 JLINK_WriteReg(MSP, 0x20001000)
T14370 000:945.741 - 0.030ms returns 0
T14370 000:945.762 JLINK_WriteReg(PSP, 0x20001000)
T14370 000:945.783 - 0.030ms returns 0
T14370 000:945.805 JLINK_WriteReg(CFBP, 0x00000000)
T14370 000:945.825 - 0.030ms returns 0
T14370 000:945.847 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T14370 000:945.868 - 0.031ms returns 0x0000000B
T14370 000:945.889 JLINK_Go()
T14370 000:945.914   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 000:948.765 - 2.894ms
T14370 000:948.798 JLINK_IsHalted()
T14370 000:949.178 - 0.392ms returns FALSE
T14370 000:949.203 JLINK_HasError()
T14370 000:950.574 JLINK_IsHalted()
T14370 000:950.870 - 0.315ms returns FALSE
T14370 000:950.904 JLINK_HasError()
T14370 000:952.630 JLINK_IsHalted()
T14370 000:952.973 - 0.362ms returns FALSE
T14370 000:953.006 JLINK_HasError()
T14370 000:954.956 JLINK_IsHalted()
T14370 000:957.473   CPU_ReadMem(2 bytes @ 0x20000000)
T14370 000:957.819 - 2.876ms returns TRUE
T14370 000:957.850 JLINK_ReadReg(R15 (PC))
T14370 000:957.872 - 0.033ms returns 0x20000000
T14370 000:957.894 JLINK_ClrBPEx(BPHandle = 0x0000000B)
T14370 000:957.916 - 0.032ms returns 0x00
T14370 000:957.938 JLINK_ReadReg(R0)
T14370 000:957.959 - 0.031ms returns 0x00000000
T14370 000:958.471 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T14370 000:958.503   Data:  70 00 00 20 FE F7 86 FE FF F7 26 FD FF F7 00 FC ...
T14370 000:958.539   CPU_WriteMem(636 bytes @ 0x20000184)
T14370 000:962.008 - 3.555ms returns 0x27C
T14370 000:962.041 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T14370 000:962.061   Data:  FA D1 20 46 01 22 BD E8 10 40 10 21 FE F7 30 BD ...
T14370 000:962.095   CPU_WriteMem(388 bytes @ 0x20000400)
T14370 000:964.440 - 2.440ms returns 0x184
T14370 000:964.512 JLINK_HasError()
T14370 000:964.562 JLINK_WriteReg(R0, 0x08001800)
T14370 000:964.613 - 0.070ms returns 0
T14370 000:964.648 JLINK_WriteReg(R1, 0x00000400)
T14370 000:964.669 - 0.035ms returns 0
T14370 000:964.695 JLINK_WriteReg(R2, 0x20000184)
T14370 000:964.716 - 0.030ms returns 0
T14370 000:964.737 JLINK_WriteReg(R3, 0x00000000)
T14370 000:964.757 - 0.030ms returns 0
T14370 000:964.779 JLINK_WriteReg(R4, 0x00000000)
T14370 000:964.799 - 0.030ms returns 0
T14370 000:964.820 JLINK_WriteReg(R5, 0x00000000)
T14370 000:964.841 - 0.030ms returns 0
T14370 000:964.862 JLINK_WriteReg(R6, 0x00000000)
T14370 000:964.882 - 0.030ms returns 0
T14370 000:964.904 JLINK_WriteReg(R7, 0x00000000)
T14370 000:964.924 - 0.030ms returns 0
T14370 000:964.949 JLINK_WriteReg(R8, 0x00000000)
T14370 000:964.970 - 0.031ms returns 0
T14370 000:964.991 JLINK_WriteReg(R9, 0x20000180)
T14370 000:965.012 - 0.030ms returns 0
T14370 000:965.033 JLINK_WriteReg(R10, 0x00000000)
T14370 000:965.053 - 0.030ms returns 0
T14370 000:965.075 JLINK_WriteReg(R11, 0x00000000)
T14370 000:965.095 - 0.030ms returns 0
T14370 000:965.116 JLINK_WriteReg(R12, 0x00000000)
T14370 000:965.136 - 0.030ms returns 0
T14370 000:965.158 JLINK_WriteReg(R13 (SP), 0x20001000)
T14370 000:965.178 - 0.030ms returns 0
T14370 000:965.199 JLINK_WriteReg(R14, 0x20000001)
T14370 000:965.220 - 0.030ms returns 0
T14370 000:965.241 JLINK_WriteReg(R15 (PC), 0x2000010C)
T14370 000:965.261 - 0.030ms returns 0
T14370 000:965.283 JLINK_WriteReg(XPSR, 0x01000000)
T14370 000:965.303 - 0.030ms returns 0
T14370 000:965.324 JLINK_WriteReg(MSP, 0x20001000)
T14370 000:965.344 - 0.030ms returns 0
T14370 000:965.366 JLINK_WriteReg(PSP, 0x20001000)
T14370 000:965.386 - 0.030ms returns 0
T14370 000:965.407 JLINK_WriteReg(CFBP, 0x00000000)
T14370 000:965.428 - 0.030ms returns 0
T14370 000:965.449 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T14370 000:965.470 - 0.031ms returns 0x0000000C
T14370 000:965.491 JLINK_Go()
T14370 000:965.515   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 000:968.326 - 2.853ms
T14370 000:968.359 JLINK_IsHalted()
T14370 000:968.641 - 0.293ms returns FALSE
T14370 000:968.664 JLINK_HasError()
T14370 000:970.945 JLINK_IsHalted()
T14370 000:971.315 - 0.411ms returns FALSE
T14370 000:971.388 JLINK_HasError()
T14370 000:973.218 JLINK_IsHalted()
T14370 000:976.270   CPU_ReadMem(2 bytes @ 0x20000000)
T14370 000:976.573 - 3.374ms returns TRUE
T14370 000:976.607 JLINK_ReadReg(R15 (PC))
T14370 000:976.631 - 0.034ms returns 0x20000000
T14370 000:976.657 JLINK_ClrBPEx(BPHandle = 0x0000000C)
T14370 000:976.679 - 0.032ms returns 0x00
T14370 000:976.701 JLINK_ReadReg(R0)
T14370 000:976.722 - 0.031ms returns 0x00000000
T14370 000:977.447 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T14370 000:977.478   Data:  0C 15 00 08 2C 1C 00 08 18 00 00 20 A0 04 00 00 ...
T14370 000:977.515   CPU_WriteMem(636 bytes @ 0x20000184)
T14370 000:981.031 - 3.603ms returns 0x27C
T14370 000:981.071 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T14370 000:981.093   Data:  FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF ...
T14370 000:981.128   CPU_WriteMem(388 bytes @ 0x20000400)
T14370 000:983.410 - 2.357ms returns 0x184
T14370 000:983.448 JLINK_HasError()
T14370 000:983.472 JLINK_WriteReg(R0, 0x08001C00)
T14370 000:983.499 - 0.037ms returns 0
T14370 000:983.523 JLINK_WriteReg(R1, 0x0000002C)
T14370 000:983.544 - 0.031ms returns 0
T14370 000:983.568 JLINK_WriteReg(R2, 0x20000184)
T14370 000:983.588 - 0.031ms returns 0
T14370 000:983.612 JLINK_WriteReg(R3, 0x00000000)
T14370 000:983.632 - 0.030ms returns 0
T14370 000:983.654 JLINK_WriteReg(R4, 0x00000000)
T14370 000:983.675 - 0.030ms returns 0
T14370 000:983.696 JLINK_WriteReg(R5, 0x00000000)
T14370 000:983.717 - 0.030ms returns 0
T14370 000:983.738 JLINK_WriteReg(R6, 0x00000000)
T14370 000:983.759 - 0.031ms returns 0
T14370 000:983.782 JLINK_WriteReg(R7, 0x00000000)
T14370 000:983.803 - 0.031ms returns 0
T14370 000:983.828 JLINK_WriteReg(R8, 0x00000000)
T14370 000:983.849 - 0.031ms returns 0
T14370 000:983.873 JLINK_WriteReg(R9, 0x20000180)
T14370 000:983.894 - 0.031ms returns 0
T14370 000:983.918 JLINK_WriteReg(R10, 0x00000000)
T14370 000:983.939 - 0.030ms returns 0
T14370 000:983.962 JLINK_WriteReg(R11, 0x00000000)
T14370 000:983.983 - 0.030ms returns 0
T14370 000:984.007 JLINK_WriteReg(R12, 0x00000000)
T14370 000:984.027 - 0.030ms returns 0
T14370 000:984.051 JLINK_WriteReg(R13 (SP), 0x20001000)
T14370 000:984.072 - 0.031ms returns 0
T14370 000:984.095 JLINK_WriteReg(R14, 0x20000001)
T14370 000:984.116 - 0.030ms returns 0
T14370 000:984.139 JLINK_WriteReg(R15 (PC), 0x2000010C)
T14370 000:984.160 - 0.031ms returns 0
T14370 000:984.183 JLINK_WriteReg(XPSR, 0x01000000)
T14370 000:984.204 - 0.031ms returns 0
T14370 000:984.231 JLINK_WriteReg(MSP, 0x20001000)
T14370 000:984.253 - 0.032ms returns 0
T14370 000:984.276 JLINK_WriteReg(PSP, 0x20001000)
T14370 000:984.297 - 0.030ms returns 0
T14370 000:984.320 JLINK_WriteReg(CFBP, 0x00000000)
T14370 000:984.341 - 0.031ms returns 0
T14370 000:984.365 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T14370 000:984.386 - 0.031ms returns 0x0000000D
T14370 000:984.410 JLINK_Go()
T14370 000:984.435   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 000:987.353 - 2.982ms
T14370 000:987.432 JLINK_IsHalted()
T14370 000:990.055   CPU_ReadMem(2 bytes @ 0x20000000)
T14370 000:990.414 - 3.008ms returns TRUE
T14370 000:990.478 JLINK_ReadReg(R15 (PC))
T14370 000:990.528 - 0.074ms returns 0x20000000
T14370 000:990.582 JLINK_ClrBPEx(BPHandle = 0x0000000D)
T14370 000:990.631 - 0.071ms returns 0x00
T14370 000:990.680 JLINK_ReadReg(R0)
T14370 000:990.702 - 0.031ms returns 0x00000000
T14370 000:990.725 JLINK_HasError()
T14370 000:990.749 JLINK_WriteReg(R0, 0x00000002)
T14370 000:990.770 - 0.030ms returns 0
T14370 000:990.793 JLINK_WriteReg(R1, 0x0000002C)
T14370 000:990.813 - 0.030ms returns 0
T14370 000:990.836 JLINK_WriteReg(R2, 0x20000184)
T14370 000:990.857 - 0.030ms returns 0
T14370 000:990.884 JLINK_WriteReg(R3, 0x00000000)
T14370 000:990.905 - 0.030ms returns 0
T14370 000:990.928 JLINK_WriteReg(R4, 0x00000000)
T14370 000:990.948 - 0.030ms returns 0
T14370 000:990.972 JLINK_WriteReg(R5, 0x00000000)
T14370 000:990.993 - 0.030ms returns 0
T14370 000:991.016 JLINK_WriteReg(R6, 0x00000000)
T14370 000:991.036 - 0.030ms returns 0
T14370 000:991.060 JLINK_WriteReg(R7, 0x00000000)
T14370 000:991.080 - 0.030ms returns 0
T14370 000:991.103 JLINK_WriteReg(R8, 0x00000000)
T14370 000:991.123 - 0.030ms returns 0
T14370 000:991.146 JLINK_WriteReg(R9, 0x20000180)
T14370 000:991.167 - 0.030ms returns 0
T14370 000:991.190 JLINK_WriteReg(R10, 0x00000000)
T14370 000:991.210 - 0.030ms returns 0
T14370 000:991.233 JLINK_WriteReg(R11, 0x00000000)
T14370 000:991.254 - 0.030ms returns 0
T14370 000:991.277 JLINK_WriteReg(R12, 0x00000000)
T14370 000:991.297 - 0.030ms returns 0
T14370 000:991.318 JLINK_WriteReg(R13 (SP), 0x20001000)
T14370 000:991.339 - 0.030ms returns 0
T14370 000:991.362 JLINK_WriteReg(R14, 0x20000001)
T14370 000:991.383 - 0.030ms returns 0
T14370 000:991.406 JLINK_WriteReg(R15 (PC), 0x20000086)
T14370 000:991.426 - 0.030ms returns 0
T14370 000:991.450 JLINK_WriteReg(XPSR, 0x01000000)
T14370 000:991.470 - 0.030ms returns 0
T14370 000:991.494 JLINK_WriteReg(MSP, 0x20001000)
T14370 000:991.514 - 0.030ms returns 0
T14370 000:991.537 JLINK_WriteReg(PSP, 0x20001000)
T14370 000:991.557 - 0.030ms returns 0
T14370 000:991.580 JLINK_WriteReg(CFBP, 0x00000000)
T14370 000:991.601 - 0.030ms returns 0
T14370 000:991.624 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T14370 000:991.645 - 0.030ms returns 0x0000000E
T14370 000:991.672 JLINK_Go()
T14370 000:991.696   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 000:994.614 - 2.981ms
T14370 000:994.685 JLINK_IsHalted()
T14370 000:997.168   CPU_ReadMem(2 bytes @ 0x20000000)
T14370 000:997.459 - 2.787ms returns TRUE
T14370 000:997.490 JLINK_ReadReg(R15 (PC))
T14370 000:997.513 - 0.033ms returns 0x20000000
T14370 000:997.537 JLINK_ClrBPEx(BPHandle = 0x0000000E)
T14370 000:997.558 - 0.031ms returns 0x00
T14370 000:997.582 JLINK_ReadReg(R0)
T14370 000:997.603 - 0.031ms returns 0x00000000
T14370 001:058.653 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
T14370 001:058.690   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T14370 001:058.737   CPU_WriteMem(388 bytes @ 0x20000000)
T14370 001:061.058 - 2.427ms returns 0x184
T14370 001:061.111 JLINK_HasError()
T14370 001:061.135 JLINK_WriteReg(R0, 0x08000000)
T14370 001:061.159 - 0.034ms returns 0
T14370 001:061.181 JLINK_WriteReg(R1, 0x017D7840)
T14370 001:061.202 - 0.032ms returns 0
T14370 001:061.225 JLINK_WriteReg(R2, 0x00000003)
T14370 001:061.246 - 0.031ms returns 0
T14370 001:061.268 JLINK_WriteReg(R3, 0x00000000)
T14370 001:061.289 - 0.034ms returns 0
T14370 001:061.315 JLINK_WriteReg(R4, 0x00000000)
T14370 001:061.336 - 0.031ms returns 0
T14370 001:061.358 JLINK_WriteReg(R5, 0x00000000)
T14370 001:061.385 - 0.037ms returns 0
T14370 001:061.407 JLINK_WriteReg(R6, 0x00000000)
T14370 001:061.428 - 0.031ms returns 0
T14370 001:061.450 JLINK_WriteReg(R7, 0x00000000)
T14370 001:061.471 - 0.031ms returns 0
T14370 001:061.493 JLINK_WriteReg(R8, 0x00000000)
T14370 001:061.514 - 0.031ms returns 0
T14370 001:061.536 JLINK_WriteReg(R9, 0x20000180)
T14370 001:061.557 - 0.031ms returns 0
T14370 001:061.579 JLINK_WriteReg(R10, 0x00000000)
T14370 001:061.600 - 0.031ms returns 0
T14370 001:061.622 JLINK_WriteReg(R11, 0x00000000)
T14370 001:061.643 - 0.031ms returns 0
T14370 001:061.665 JLINK_WriteReg(R12, 0x00000000)
T14370 001:061.686 - 0.031ms returns 0
T14370 001:061.708 JLINK_WriteReg(R13 (SP), 0x20001000)
T14370 001:061.729 - 0.032ms returns 0
T14370 001:061.752 JLINK_WriteReg(R14, 0x20000001)
T14370 001:061.772 - 0.031ms returns 0
T14370 001:061.794 JLINK_WriteReg(R15 (PC), 0x20000054)
T14370 001:061.816 - 0.032ms returns 0
T14370 001:061.842 JLINK_WriteReg(XPSR, 0x01000000)
T14370 001:061.863 - 0.030ms returns 0
T14370 001:061.884 JLINK_WriteReg(MSP, 0x20001000)
T14370 001:061.905 - 0.030ms returns 0
T14370 001:061.926 JLINK_WriteReg(PSP, 0x20001000)
T14370 001:061.947 - 0.030ms returns 0
T14370 001:061.968 JLINK_WriteReg(CFBP, 0x00000000)
T14370 001:061.989 - 0.030ms returns 0
T14370 001:062.011 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T14370 001:062.034   CPU_ReadMem(2 bytes @ 0x20000000)
T14370 001:062.364 - 0.395ms returns 0x0000000F
T14370 001:062.438 JLINK_Go()
T14370 001:062.490   CPU_WriteMem(2 bytes @ 0x20000000)
T14370 001:062.932   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 001:065.808 - 3.389ms
T14370 001:065.841 JLINK_IsHalted()
T14370 001:068.313   CPU_ReadMem(2 bytes @ 0x20000000)
T14370 001:068.611 - 2.789ms returns TRUE
T14370 001:068.646 JLINK_ReadReg(R15 (PC))
T14370 001:068.669 - 0.033ms returns 0x20000000
T14370 001:068.692 JLINK_ClrBPEx(BPHandle = 0x0000000F)
T14370 001:068.713 - 0.032ms returns 0x00
T14370 001:068.735 JLINK_ReadReg(R0)
T14370 001:068.757 - 0.031ms returns 0x00000000
T14370 001:068.779 JLINK_HasError()
T14370 001:068.802 JLINK_WriteReg(R0, 0xFFFFFFFF)
T14370 001:068.824 - 0.036ms returns 0
T14370 001:068.849 JLINK_WriteReg(R1, 0x08000000)
T14370 001:068.870 - 0.030ms returns 0
T14370 001:068.898 JLINK_WriteReg(R2, 0x00001C2C)
T14370 001:068.918 - 0.030ms returns 0
T14370 001:068.940 JLINK_WriteReg(R3, 0x04C11DB7)
T14370 001:068.960 - 0.030ms returns 0
T14370 001:068.982 JLINK_WriteReg(R4, 0x00000000)
T14370 001:069.002 - 0.030ms returns 0
T14370 001:069.024 JLINK_WriteReg(R5, 0x00000000)
T14370 001:069.044 - 0.030ms returns 0
T14370 001:069.065 JLINK_WriteReg(R6, 0x00000000)
T14370 001:069.086 - 0.030ms returns 0
T14370 001:069.107 JLINK_WriteReg(R7, 0x00000000)
T14370 001:069.128 - 0.030ms returns 0
T14370 001:069.149 JLINK_WriteReg(R8, 0x00000000)
T14370 001:069.170 - 0.030ms returns 0
T14370 001:069.191 JLINK_WriteReg(R9, 0x20000180)
T14370 001:069.212 - 0.030ms returns 0
T14370 001:069.233 JLINK_WriteReg(R10, 0x00000000)
T14370 001:069.253 - 0.030ms returns 0
T14370 001:069.275 JLINK_WriteReg(R11, 0x00000000)
T14370 001:069.295 - 0.030ms returns 0
T14370 001:069.317 JLINK_WriteReg(R12, 0x00000000)
T14370 001:069.337 - 0.030ms returns 0
T14370 001:069.358 JLINK_WriteReg(R13 (SP), 0x20001000)
T14370 001:069.379 - 0.030ms returns 0
T14370 001:069.401 JLINK_WriteReg(R14, 0x20000001)
T14370 001:069.421 - 0.030ms returns 0
T14370 001:069.443 JLINK_WriteReg(R15 (PC), 0x20000002)
T14370 001:069.463 - 0.030ms returns 0
T14370 001:069.484 JLINK_WriteReg(XPSR, 0x01000000)
T14370 001:069.583 - 0.122ms returns 0
T14370 001:069.635 JLINK_WriteReg(MSP, 0x20001000)
T14370 001:069.681 - 0.069ms returns 0
T14370 001:069.730 JLINK_WriteReg(PSP, 0x20001000)
T14370 001:069.776 - 0.069ms returns 0
T14370 001:069.824 JLINK_WriteReg(CFBP, 0x00000000)
T14370 001:069.863 - 0.050ms returns 0
T14370 001:069.886 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T14370 001:069.908 - 0.032ms returns 0x00000010
T14370 001:069.930 JLINK_Go()
T14370 001:069.955   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 001:072.714 - 2.802ms
T14370 001:072.747 JLINK_IsHalted()
T14370 001:073.030 - 0.320ms returns FALSE
T14370 001:073.096 JLINK_HasError()
T14370 001:076.925 JLINK_IsHalted()
T14370 001:077.242 - 0.329ms returns FALSE
T14370 001:077.267 JLINK_HasError()
T14370 001:078.941 JLINK_IsHalted()
T14370 001:079.263 - 0.361ms returns FALSE
T14370 001:079.333 JLINK_HasError()
T14370 001:081.015 JLINK_IsHalted()
T14370 001:081.348 - 0.351ms returns FALSE
T14370 001:081.381 JLINK_HasError()
T14370 001:083.010 JLINK_IsHalted()
T14370 001:083.338 - 0.341ms returns FALSE
T14370 001:083.364 JLINK_HasError()
T14370 001:085.011 JLINK_IsHalted()
T14370 001:085.346 - 0.347ms returns FALSE
T14370 001:085.371 JLINK_HasError()
T14370 001:087.052 JLINK_IsHalted()
T14370 001:087.417 - 0.403ms returns FALSE
T14370 001:087.485 JLINK_HasError()
T14370 001:089.158 JLINK_IsHalted()
T14370 001:089.459 - 0.313ms returns FALSE
T14370 001:089.484 JLINK_HasError()
T14370 001:090.809 JLINK_IsHalted()
T14370 001:091.158 - 0.367ms returns FALSE
T14370 001:091.191 JLINK_HasError()
T14370 001:092.820 JLINK_IsHalted()
T14370 001:093.152 - 0.352ms returns FALSE
T14370 001:093.186 JLINK_HasError()
T14370 001:094.833 JLINK_IsHalted()
T14370 001:095.198 - 0.402ms returns FALSE
T14370 001:095.265 JLINK_HasError()
T14370 001:097.361 JLINK_IsHalted()
T14370 001:097.656 - 0.313ms returns FALSE
T14370 001:097.689 JLINK_HasError()
T14370 001:099.370 JLINK_IsHalted()
T14370 001:099.727 - 0.397ms returns FALSE
T14370 001:099.808 JLINK_HasError()
T14370 001:101.738 JLINK_IsHalted()
T14370 001:102.073 - 0.350ms returns FALSE
T14370 001:102.102 JLINK_HasError()
T14370 001:103.729 JLINK_IsHalted()
T14370 001:104.075 - 0.382ms returns FALSE
T14370 001:104.142 JLINK_HasError()
T14370 001:106.061 JLINK_IsHalted()
T14370 001:106.376 - 0.326ms returns FALSE
T14370 001:106.400 JLINK_HasError()
T14370 001:108.061 JLINK_IsHalted()
T14370 001:108.384 - 0.362ms returns FALSE
T14370 001:108.455 JLINK_HasError()
T14370 001:110.078 JLINK_IsHalted()
T14370 001:110.414 - 0.354ms returns FALSE
T14370 001:110.446 JLINK_HasError()
T14370 001:112.063 JLINK_IsHalted()
T14370 001:112.356 - 0.312ms returns FALSE
T14370 001:112.389 JLINK_HasError()
T14370 001:115.069 JLINK_IsHalted()
T14370 001:115.418 - 0.368ms returns FALSE
T14370 001:115.452 JLINK_HasError()
T14370 001:117.076 JLINK_IsHalted()
T14370 001:119.656   CPU_ReadMem(2 bytes @ 0x20000000)
T14370 001:119.961 - 2.897ms returns TRUE
T14370 001:119.986 JLINK_ReadReg(R15 (PC))
T14370 001:120.009 - 0.033ms returns 0x20000000
T14370 001:120.031 JLINK_ClrBPEx(BPHandle = 0x00000010)
T14370 001:120.052 - 0.031ms returns 0x00
T14370 001:120.074 JLINK_ReadReg(R0)
T14370 001:120.095 - 0.031ms returns 0xDE5BBB18
T14370 001:120.780 JLINK_HasError()
T14370 001:120.811 JLINK_WriteReg(R0, 0x00000003)
T14370 001:120.833 - 0.032ms returns 0
T14370 001:120.855 JLINK_WriteReg(R1, 0x08000000)
T14370 001:120.876 - 0.031ms returns 0
T14370 001:120.898 JLINK_WriteReg(R2, 0x00001C2C)
T14370 001:120.918 - 0.032ms returns 0
T14370 001:120.943 JLINK_WriteReg(R3, 0x04C11DB7)
T14370 001:120.963 - 0.030ms returns 0
T14370 001:120.984 JLINK_WriteReg(R4, 0x00000000)
T14370 001:121.004 - 0.030ms returns 0
T14370 001:121.025 JLINK_WriteReg(R5, 0x00000000)
T14370 001:121.045 - 0.030ms returns 0
T14370 001:121.066 JLINK_WriteReg(R6, 0x00000000)
T14370 001:121.087 - 0.030ms returns 0
T14370 001:121.108 JLINK_WriteReg(R7, 0x00000000)
T14370 001:121.128 - 0.030ms returns 0
T14370 001:121.149 JLINK_WriteReg(R8, 0x00000000)
T14370 001:121.169 - 0.030ms returns 0
T14370 001:121.190 JLINK_WriteReg(R9, 0x20000180)
T14370 001:121.210 - 0.030ms returns 0
T14370 001:121.231 JLINK_WriteReg(R10, 0x00000000)
T14370 001:121.251 - 0.030ms returns 0
T14370 001:121.277 JLINK_WriteReg(R11, 0x00000000)
T14370 001:121.299 - 0.032ms returns 0
T14370 001:121.321 JLINK_WriteReg(R12, 0x00000000)
T14370 001:121.341 - 0.030ms returns 0
T14370 001:121.362 JLINK_WriteReg(R13 (SP), 0x20001000)
T14370 001:121.383 - 0.031ms returns 0
T14370 001:121.405 JLINK_WriteReg(R14, 0x20000001)
T14370 001:121.425 - 0.030ms returns 0
T14370 001:121.446 JLINK_WriteReg(R15 (PC), 0x20000086)
T14370 001:121.467 - 0.030ms returns 0
T14370 001:121.488 JLINK_WriteReg(XPSR, 0x01000000)
T14370 001:121.508 - 0.030ms returns 0
T14370 001:121.529 JLINK_WriteReg(MSP, 0x20001000)
T14370 001:121.549 - 0.030ms returns 0
T14370 001:121.571 JLINK_WriteReg(PSP, 0x20001000)
T14370 001:121.591 - 0.030ms returns 0
T14370 001:121.612 JLINK_WriteReg(CFBP, 0x00000000)
T14370 001:121.633 - 0.030ms returns 0
T14370 001:121.654 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T14370 001:121.675 - 0.031ms returns 0x00000011
T14370 001:121.696 JLINK_Go()
T14370 001:121.722   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 001:124.625 - 2.960ms
T14370 001:124.684 JLINK_IsHalted()
T14370 001:127.245   CPU_ReadMem(2 bytes @ 0x20000000)
T14370 001:127.566 - 2.893ms returns TRUE
T14370 001:127.595 JLINK_ReadReg(R15 (PC))
T14370 001:127.618 - 0.033ms returns 0x20000000
T14370 001:127.642 JLINK_ClrBPEx(BPHandle = 0x00000011)
T14370 001:127.664 - 0.031ms returns 0x00
T14370 001:127.688 JLINK_ReadReg(R0)
T14370 001:127.709 - 0.031ms returns 0x00000000
T14370 001:183.395 JLINK_WriteMemEx(0x20000000, 0x00000002 Bytes, Flags = 0x02000000)
T14370 001:183.448   Data:  FE E7
T14370 001:183.495   CPU_WriteMem(2 bytes @ 0x20000000)
T14370 001:183.849 - 0.473ms returns 0x2
T14370 001:183.884 JLINK_HasError()
T14370 001:183.906 JLINK_HasError()
T14370 001:183.929 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T14370 001:183.953 - 0.034ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T14370 001:183.976 JLINK_Reset()
T14370 001:184.002   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T14370 001:184.351   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T14370 001:187.034   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T14370 001:192.689   Reset: Reset device via AIRCR.SYSRESETREQ.
T14370 001:192.740   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T14370 001:245.642   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T14370 001:245.935   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T14370 001:246.224   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T14370 001:246.544   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T14370 001:252.205   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T14370 001:255.030   CPU_WriteMem(4 bytes @ 0x********)
T14370 001:255.396   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T14370 001:255.685   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 001:255.960 - 71.998ms
T14370 001:255.996 JLINK_Go()
T14370 001:256.024   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 001:256.340   CPU_WriteMem(4 bytes @ 0xE0002008)
T14370 001:256.372   CPU_WriteMem(4 bytes @ 0xE000200C)
T14370 001:256.403   CPU_WriteMem(4 bytes @ 0xE0002010)
T14370 001:256.433   CPU_WriteMem(4 bytes @ 0xE0002014)
T14370 001:256.463   CPU_WriteMem(4 bytes @ 0xE0002018)
T14370 001:256.494   CPU_WriteMem(4 bytes @ 0xE000201C)
T14370 001:257.808   CPU_WriteMem(4 bytes @ 0xE0001004)
T14370 001:258.593 - 2.614ms
T14370 001:270.396 JLINK_Close()
T14370 001:270.643   CPU is running
T14370 001:270.688   CPU_WriteMem(4 bytes @ 0xE0002008)
T14370 001:270.986   CPU is running
T14370 001:271.028   CPU_WriteMem(4 bytes @ 0xE000200C)
T14370 001:271.364   CPU is running
T14370 001:271.405   CPU_WriteMem(4 bytes @ 0xE0002010)
T14370 001:271.706   CPU is running
T14370 001:271.746   CPU_WriteMem(4 bytes @ 0xE0002014)
T14370 001:272.055   CPU is running
T14370 001:272.104   CPU_WriteMem(4 bytes @ 0xE0002018)
T14370 001:272.416   CPU is running
T14370 001:272.494   CPU_WriteMem(4 bytes @ 0xE000201C)
T14370 001:278.564   OnDisconnectTarget() start
T14370 001:278.606    J-Link Script File: Executing OnDisconnectTarget()
T14370 001:278.633   CPU_WriteMem(4 bytes @ 0xE0042004)
T14370 001:278.950   CPU_WriteMem(4 bytes @ 0xE0042008)
T14370 001:283.965   OnDisconnectTarget() end
T14370 001:284.014   CPU_ReadMem(4 bytes @ 0xE0001000)
T14370 001:303.530 - 33.172ms
T14370 001:303.580   
T14370 001:303.601   Closed

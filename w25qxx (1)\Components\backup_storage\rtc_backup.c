#include "rtc_backup.h"
#include <string.h>

/**
 * @brief 初始化RTC备份域
 * @retval HAL状态
 */
HAL_StatusTypeDef backup_init(void)
{
    /* 启用PWR时钟 */
    __HAL_RCC_PWR_CLK_ENABLE();
    
    /* 启用对备份域的写访问 */
    HAL_PWR_EnableBkUpAccess();
    
    /* 检查是否首次使用 */
    if (BACKUP_READ_REG(BACKUP_REG_MAGIC) != BACKUP_MAGIC_VALUE) {
        /* 首次使用，清空所有备份寄存器 */
        backup_clear_data();
        
        /* 写入魔数和版本 */
        BACKUP_WRITE_REG(BACKUP_REG_MAGIC, BACKUP_MAGIC_VALUE);
        BACKUP_WRITE_REG(BACKUP_REG_VERSION, BACKUP_VERSION);
    }
    
    return HAL_OK;
}

/**
 * @brief 计算数据校验和
 * @param data 数据指针
 * @retval 校验和
 */
uint32_t backup_calculate_checksum(const backup_data_t* data)
{
    uint32_t checksum = 0;
    const uint32_t* ptr = (const uint32_t*)data;
    
    for (int i = 0; i < sizeof(backup_data_t) / 4; i++) {
        checksum ^= ptr[i];
    }
    
    return checksum;
}

/**
 * @brief 写入数据到备份寄存器
 * @param data 要写入的数据
 * @retval HAL状态
 */
HAL_StatusTypeDef backup_write_data(const backup_data_t* data)
{
    if (data == NULL) {
        return HAL_ERROR;
    }
    
    /* 计算校验和 */
    uint32_t checksum = backup_calculate_checksum(data);
    
    /* 写入校验和 */
    BACKUP_WRITE_REG(BACKUP_REG_CHECKSUM, checksum);
    
    /* 写入数据 */
    const uint32_t* src = (const uint32_t*)data;
    for (int i = 0; i < sizeof(backup_data_t) / 4; i++) {
        BACKUP_WRITE_REG(BACKUP_REG_DATA_START + i, src[i]);
    }
    
    return HAL_OK;
}

/**
 * @brief 从备份寄存器读取数据
 * @param data 读取数据的缓冲区
 * @retval HAL状态
 */
HAL_StatusTypeDef backup_read_data(backup_data_t* data)
{
    if (data == NULL) {
        return HAL_ERROR;
    }
    
    /* 检查数据有效性 */
    if (!backup_is_data_valid()) {
        return HAL_ERROR;
    }
    
    /* 读取数据 */
    uint32_t* dst = (uint32_t*)data;
    for (int i = 0; i < sizeof(backup_data_t) / 4; i++) {
        dst[i] = BACKUP_READ_REG(BACKUP_REG_DATA_START + i);
    }
    
    return HAL_OK;
}

/**
 * @brief 检查备份数据是否有效
 * @retval 1-有效, 0-无效
 */
uint8_t backup_is_data_valid(void)
{
    /* 检查魔数 */
    if (BACKUP_READ_REG(BACKUP_REG_MAGIC) != BACKUP_MAGIC_VALUE) {
        return 0;
    }
    
    /* 读取数据并验证校验和 */
    backup_data_t temp_data;
    uint32_t* dst = (uint32_t*)&temp_data;
    for (int i = 0; i < sizeof(backup_data_t) / 4; i++) {
        dst[i] = BACKUP_READ_REG(BACKUP_REG_DATA_START + i);
    }
    
    uint32_t stored_checksum = BACKUP_READ_REG(BACKUP_REG_CHECKSUM);
    uint32_t calculated_checksum = backup_calculate_checksum(&temp_data);
    
    return (stored_checksum == calculated_checksum) ? 1 : 0;
}

/**
 * @brief 清空备份数据
 * @retval HAL状态
 */
HAL_StatusTypeDef backup_clear_data(void)
{
    /* 清空所有数据寄存器 */
    for (int i = BACKUP_REG_DATA_START; i < BACKUP_REG_DATA_START + 17; i++) {
        BACKUP_WRITE_REG(i, 0);
    }
    
    /* 清空校验和 */
    BACKUP_WRITE_REG(BACKUP_REG_CHECKSUM, 0);
    
    return HAL_OK;
}

/**
 * @brief 备份数据使用示例
 */
void backup_example_usage(void)
{
    backup_data_t my_data;
    
    /* 初始化备份系统 */
    backup_init();
    
    /* 准备要保存的数据 */
    my_data.system_config = 0x12345678;
    my_data.user_settings[0] = 100;
    my_data.user_settings[1] = 200;
    my_data.sensor_data[0] = 1234;
    my_data.status_flags = 0xABCD;
    my_data.timestamp = HAL_GetTick();
    
    /* 写入数据 */
    if (backup_write_data(&my_data) == HAL_OK) {
        printf("数据已保存到备份寄存器\r\n");
    }
    
    /* 读取数据 */
    backup_data_t read_data;
    if (backup_read_data(&read_data) == HAL_OK) {
        printf("从备份寄存器读取数据成功\r\n");
        printf("系统配置: 0x%08X\r\n", read_data.system_config);
        printf("用户设置1: %d\r\n", read_data.user_settings[0]);
    }
}

#ifndef EEPROM_I2C_H
#define EEPROM_I2C_H

#include "main.h"
#include "i2c.h"

/* 外部EEPROM存储方案 */
/* 支持常见的I2C EEPROM芯片 */

/* 常见EEPROM芯片配置 */
#define EEPROM_24C02    0   /* 256字节 */
#define EEPROM_24C04    1   /* 512字节 */
#define EEPROM_24C08    2   /* 1KB */
#define EEPROM_24C16    3   /* 2KB */
#define EEPROM_24C32    4   /* 4KB */
#define EEPROM_24C64    5   /* 8KB */
#define EEPROM_24C128   6   /* 16KB */
#define EEPROM_24C256   7   /* 32KB */
#define EEPROM_24C512   8   /* 64KB */

/* 选择使用的EEPROM型号 */
#define EEPROM_TYPE     EEPROM_24C256

/* EEPROM配置表 */
typedef struct {
    uint16_t size;          /* 容量(字节) */
    uint8_t page_size;      /* 页大小 */
    uint8_t addr_bytes;     /* 地址字节数 */
    uint16_t write_time;    /* 写入时间(ms) */
} eeprom_config_t;

/* EEPROM I2C地址 */
#define EEPROM_I2C_ADDR     0xA0    /* 基础地址 */

/* 根据EEPROM类型获取配置 */
extern const eeprom_config_t eeprom_configs[];

/* 数据结构定义 */
typedef struct {
    uint32_t magic;         /* 魔数验证 */
    uint32_t version;       /* 数据版本 */
    uint32_t data_size;     /* 数据大小 */
    uint32_t checksum;      /* 校验和 */
    uint32_t write_count;   /* 写入次数 */
    uint32_t timestamp;     /* 时间戳 */
} eeprom_header_t;

#define EEPROM_MAGIC_VALUE  0xABCDEF12
#define EEPROM_VERSION      0x0001

/* 函数声明 */
HAL_StatusTypeDef eeprom_init(void);
HAL_StatusTypeDef eeprom_write_byte(uint16_t addr, uint8_t data);
HAL_StatusTypeDef eeprom_read_byte(uint16_t addr, uint8_t* data);
HAL_StatusTypeDef eeprom_write_buffer(uint16_t addr, const uint8_t* data, uint16_t size);
HAL_StatusTypeDef eeprom_read_buffer(uint16_t addr, uint8_t* data, uint16_t size);
HAL_StatusTypeDef eeprom_write_data_with_header(const uint8_t* data, uint16_t size);
HAL_StatusTypeDef eeprom_read_data_with_header(uint8_t* data, uint16_t* size);
uint8_t eeprom_is_data_valid(void);
HAL_StatusTypeDef eeprom_format(void);

/* 辅助函数 */
uint8_t eeprom_get_device_addr(uint16_t mem_addr);
uint16_t eeprom_get_page_size(void);
uint16_t eeprom_get_total_size(void);
uint32_t eeprom_calculate_checksum(const uint8_t* data, uint16_t size);

/* I2C句柄 - 需要在main.c中定义 */
extern I2C_HandleTypeDef hi2c1;
#define EEPROM_I2C_HANDLE   &hi2c1

/* 超时定义 */
#define EEPROM_TIMEOUT      1000

#endif /* EEPROM_I2C_H */

/**
 * @file oled_app.c
 * @brief OLED应用层实现
 * <AUTHOR>
 * @date 2025-01-02
 */

#include "oled_app.h"

// OLED初始化状态标志
static uint8_t oled_init_status = 0;  // 0: 未初始化, 1: 初始化成功, 2: 初始化失败

/**
 * @brief OLED应用初始化
 * @param 无
 * @return 无
 * @note 初始化OLED硬件，清屏并设置显示参数
 */
void oled_app_init(void)
{
    // 延时确保I2C总线稳定
    HAL_Delay(100);
    
    // 初始化OLED
    OLED_Init();
    
    // 延时等待初始化完成
    HAL_Delay(50);
    
    // 清屏
    OLED_Clear();
    
    // 显示开启
    OLED_Display_On();
    
    // 显示初始化信息
    OLED_ShowString(0, 0, "OLED Init OK", 12, 0);
    OLED_ShowString(0, 2, "Yaw:", 12, 0);
    OLED_ShowString(0, 4, "GyroZ:", 12, 0);
    
    // 设置初始化成功标志
    oled_init_status = 1;
    
    // 延时显示初始化信息
    HAL_Delay(1000);
    
    // 清屏准备显示数据
    OLED_Clear();
}

/**
 * @brief OLED显示任务
 * @param 无  
 * @return 无
 * @note 周期性调用，更新OLED显示内容
 */
void oled_app_task(void)
{
    // 检查OLED是否初始化成功
    if (oled_init_status != 1) {
        return;
    }
    
    // 显示debug数据
    oled_display_debug_data();
}

/**
 * @brief 显示debug数据
 * @param 无
 * @return 无
 * @note 显示debug_yaw和debug_gyro_z数据
 */
void oled_display_debug_data(void)
{
   
    // 显示数据计数
    OLED_ShowString(0, 0, "Bai Heng:", 16, 0);
   
}

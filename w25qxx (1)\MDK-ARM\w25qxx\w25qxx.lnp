--cpu=Cortex-M4.fp.sp
"w25qxx\startup_stm32f407xx.o"
"w25qxx\main.o"
"w25qxx\gpio.o"
"w25qxx\dma.o"
"w25qxx\spi.o"
"w25qxx\usart.o"
"w25qxx\stm32f4xx_it.o"
"w25qxx\stm32f4xx_hal_msp.o"
"w25qxx\stm32f4xx_hal_spi.o"
"w25qxx\stm32f4xx_hal_rcc.o"
"w25qxx\stm32f4xx_hal_rcc_ex.o"
"w25qxx\stm32f4xx_hal_flash.o"
"w25qxx\stm32f4xx_hal_flash_ex.o"
"w25qxx\stm32f4xx_hal_flash_ramfunc.o"
"w25qxx\stm32f4xx_hal_gpio.o"
"w25qxx\stm32f4xx_hal_dma_ex.o"
"w25qxx\stm32f4xx_hal_dma.o"
"w25qxx\stm32f4xx_hal_pwr.o"
"w25qxx\stm32f4xx_hal_pwr_ex.o"
"w25qxx\stm32f4xx_hal_cortex.o"
"w25qxx\stm32f4xx_hal.o"
"w25qxx\stm32f4xx_hal_exti.o"
"w25qxx\stm32f4xx_hal_uart.o"
"w25qxx\system_stm32f4xx.o"
"w25qxx\gd25qxx.o"
--library_type=microlib --strict --scatter "w25qxx\w25qxx.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "w25qxx.map" -o w25qxx\w25qxx.axf
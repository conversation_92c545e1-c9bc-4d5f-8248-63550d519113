#include "flash_storage_test.h"
#include "Components/backup_storage/internal_flash.h"
#include <stdio.h>
#include <string.h>

/**
 * @brief 车辆系统配置结构
 * @note 替代外部Flash存储的车辆配置数据
 */
typedef struct {
    uint32_t motor_speed;           /* 电机转速设定 */
    uint32_t steering_angle;        /* 转向角度 */
    uint32_t sensor_threshold[4];   /* 传感器阈值 */
    uint32_t control_mode;          /* 控制模式 */
    uint32_t error_flags;           /* 错误标志位 */
    char vehicle_id[16];            /* 车辆ID */
    uint32_t boot_count;            /* 启动次数 */
    uint32_t total_runtime;         /* 总运行时间 */
} vehicle_config_t;

/* 全局车辆配置 */
static vehicle_config_t g_vehicle_config;
static uint8_t config_loaded = 0;

/**
 * @brief 加载车辆配置
 * @retval 0-成功, 1-失败
 */
uint8_t load_vehicle_config(void)
{
    printf("=== 加载车辆配置 ===\r\n");
    
    /* 初始化Flash存储 */
    if (internal_flash_init() != HAL_OK) {
        printf("✗ Flash存储初始化失败\r\n");
        return 1;
    }
    
    /* 检查是否有有效的保存配置 */
    if (internal_flash_is_data_valid()) {
        printf("✓ 检测到有效的保存配置，正在恢复...\r\n");
        
        uint32_t size = sizeof(g_vehicle_config);
        if (internal_flash_read_data((uint8_t*)&g_vehicle_config, &size) == HAL_OK) {
            printf("✓ 车辆配置恢复成功:\r\n");
            printf("  - 电机转速: %d RPM\r\n", (int)g_vehicle_config.motor_speed);
            printf("  - 转向角度: %d 度\r\n", (int)g_vehicle_config.steering_angle);
            printf("  - 控制模式: %d\r\n", (int)g_vehicle_config.control_mode);
            printf("  - 车辆ID: %s\r\n", g_vehicle_config.vehicle_id);
            printf("  - 启动次数: %d\r\n", (int)g_vehicle_config.boot_count);
            printf("  - 总运行时间: %d 秒\r\n", (int)g_vehicle_config.total_runtime);
            
            /* 更新启动次数 */
            g_vehicle_config.boot_count++;
            config_loaded = 1;
            return 0;
        }
    }
    
    /* 没有有效配置，使用默认值 */
    printf("! 未找到有效配置，使用默认设置\r\n");
    g_vehicle_config.motor_speed = 1000;
    g_vehicle_config.steering_angle = 0;
    g_vehicle_config.sensor_threshold[0] = 1024;  /* 前方传感器 */
    g_vehicle_config.sensor_threshold[1] = 512;   /* 左侧传感器 */
    g_vehicle_config.sensor_threshold[2] = 512;   /* 右侧传感器 */
    g_vehicle_config.sensor_threshold[3] = 2048;  /* 后方传感器 */
    g_vehicle_config.control_mode = 1;            /* 自动模式 */
    g_vehicle_config.error_flags = 0;
    strcpy(g_vehicle_config.vehicle_id, "CAR_F407");
    g_vehicle_config.boot_count = 1;
    g_vehicle_config.total_runtime = 0;
    
    config_loaded = 1;
    printf("✓ 默认配置已加载\r\n");
    return 0;
}

/**
 * @brief 保存车辆配置
 * @retval 0-成功, 1-失败
 */
uint8_t save_vehicle_config(void)
{
    if (!config_loaded) {
        printf("配置未加载，无法保存\r\n");
        return 1;
    }
    
    printf("正在保存车辆配置...\r\n");
    
    /* 写入Flash */
    if (internal_flash_write_data((uint8_t*)&g_vehicle_config, sizeof(g_vehicle_config)) == HAL_OK) {
        printf("✓ 车辆配置保存成功\r\n");
        return 0;
    } else {
        printf("✗ 车辆配置保存失败\r\n");
        return 1;
    }
}

/**
 * @brief 更新电机转速配置
 * @param speed 新的转速值
 */
void update_motor_speed(uint32_t speed)
{
    if (!config_loaded) return;
    
    printf("更新电机转速: %d -> %d RPM\r\n", (int)g_vehicle_config.motor_speed, (int)speed);
    g_vehicle_config.motor_speed = speed;
    
    /* 立即保存重要配置更改 */
    save_vehicle_config();
}

/**
 * @brief 更新控制模式
 * @param mode 控制模式 (0-手动, 1-自动, 2-跟随)
 */
void update_control_mode(uint32_t mode)
{
    if (!config_loaded) return;
    
    const char* mode_names[] = {"手动", "自动", "跟随"};
    printf("更新控制模式: %s -> %s\r\n", 
           mode_names[g_vehicle_config.control_mode], 
           mode_names[mode]);
    
    g_vehicle_config.control_mode = mode;
    save_vehicle_config();
}

/**
 * @brief 更新传感器阈值
 * @param sensor_id 传感器ID (0-前, 1-左, 2-右, 3-后)
 * @param threshold 新的阈值
 */
void update_sensor_threshold(uint8_t sensor_id, uint32_t threshold)
{
    if (!config_loaded || sensor_id >= 4) return;
    
    const char* sensor_names[] = {"前方", "左侧", "右侧", "后方"};
    printf("更新%s传感器阈值: %d -> %d\r\n", 
           sensor_names[sensor_id],
           (int)g_vehicle_config.sensor_threshold[sensor_id], 
           (int)threshold);
    
    g_vehicle_config.sensor_threshold[sensor_id] = threshold;
    save_vehicle_config();
}

/**
 * @brief 获取当前车辆配置
 * @retval 车辆配置指针
 */
const vehicle_config_t* get_vehicle_config(void)
{
    return config_loaded ? &g_vehicle_config : NULL;
}

/**
 * @brief 车辆系统关键操作前的状态保存
 */
void save_state_before_critical_operation(void)
{
    if (!config_loaded) return;
    
    printf("关键操作前保存状态...\r\n");
    
    /* 设置关键操作标志 */
    g_vehicle_config.error_flags |= 0x8000;
    save_vehicle_config();
}

/**
 * @brief 车辆系统关键操作后的状态更新
 */
void update_state_after_critical_operation(void)
{
    if (!config_loaded) return;
    
    printf("关键操作完成，更新状态...\r\n");
    
    /* 清除关键操作标志 */
    g_vehicle_config.error_flags &= ~0x8000;
    save_vehicle_config();
}

/**
 * @brief 更新运行时间 (建议在定时器中调用)
 */
void update_runtime(void)
{
    if (!config_loaded) return;
    
    static uint32_t last_update_tick = 0;
    uint32_t current_tick = HAL_GetTick();
    
    /* 每秒更新一次运行时间 */
    if (current_tick - last_update_tick >= 1000) {
        g_vehicle_config.total_runtime++;
        last_update_tick = current_tick;
        
        /* 每10分钟保存一次运行时间 */
        if (g_vehicle_config.total_runtime % 600 == 0) {
            printf("运行时间: %d 秒，正在保存...\r\n", (int)g_vehicle_config.total_runtime);
            save_vehicle_config();
        }
    }
}

/**
 * @brief 车辆Flash存储测试
 */
void test_vehicle_flash_storage(void)
{
    printf("\r\n========== 车辆Flash存储测试 ==========\r\n");
    
    /* 1. 加载配置 */
    if (load_vehicle_config() == 0) {
        printf("✓ 配置加载成功\r\n");
    } else {
        printf("✗ 配置加载失败\r\n");
        return;
    }
    
    /* 2. 显示当前配置 */
    const vehicle_config_t* config = get_vehicle_config();
    if (config) {
        printf("\r\n当前车辆配置:\r\n");
        printf("  电机转速: %d RPM\r\n", (int)config->motor_speed);
        printf("  转向角度: %d 度\r\n", (int)config->steering_angle);
        printf("  控制模式: %d\r\n", (int)config->control_mode);
        printf("  传感器阈值: [%d, %d, %d, %d]\r\n", 
               (int)config->sensor_threshold[0], (int)config->sensor_threshold[1],
               (int)config->sensor_threshold[2], (int)config->sensor_threshold[3]);
        printf("  车辆ID: %s\r\n", config->vehicle_id);
        printf("  启动次数: %d\r\n", (int)config->boot_count);
        printf("  总运行时间: %d 秒\r\n", (int)config->total_runtime);
    }
    
    /* 3. 测试配置更新 */
    printf("\r\n--- 测试配置更新 ---\r\n");
    update_motor_speed(1500);
    update_control_mode(2);
    update_sensor_threshold(0, 1500);
    
    /* 4. 测试关键操作保护 */
    printf("\r\n--- 测试关键操作保护 ---\r\n");
    save_state_before_critical_operation();
    printf("执行关键操作 (模拟延时)...\r\n");
    HAL_Delay(100);
    update_state_after_critical_operation();
    
    /* 5. 模拟运行时间更新 */
    printf("\r\n--- 模拟运行时间更新 ---\r\n");
    for (int i = 0; i < 5; i++) {
        HAL_Delay(1000);
        update_runtime();
        printf("运行时间: %d 秒\r\n", (int)get_vehicle_config()->total_runtime);
    }
    
    printf("\r\n========== 车辆Flash存储测试完成 ==========\r\n");
    printf("✓ 内部Flash存储方案已就绪！\r\n");
    printf("✓ 可以替代损坏的外部Flash进行数据保存\r\n");
}
